{"timestamp": "2025-07-07T06:37:39.191Z", "summary": {"ready": false, "externalLinksCount": 2, "localResourcesCount": 7}, "externalLinks": [{"file": "./public/local-resources/schema-org-reference.html", "matches": "128:  \"@context\": \"https://schema.org\",\n131:  \"url\": \"https://ai.ynnx.com\",\n132:  \"logo\": \"https://ai.ynnx.com/logo.png\","}, {"file": "dist/*", "matches": "./dist/webfonts/fa-regular-400.ttf\n./dist/webfonts/fa-brands-400.ttf\n./dist/webfonts/fa-solid-900.ttf\n./dist/local-resources/api-reference.html\n./dist/index.html\n./dist/downloads/tools/jetbrains-ai-assistant/latest/ml-llm-252.21735.40.zip\n./dist/docs/faq/common-faq.md\n./dist/assets/components-legacy-DfahuXQK.js\n./dist/assets/webfonts/fa-regular-400.ttf\n./dist/assets/webfonts/fa-brands-400.ttf\n./dist/assets/webfonts/fa-solid-900.ttf\n./dist/assets/vendor-react-dom-legacy-DyXf2unF.js\n./dist/assets/vendor-legacy-SWB_buFX.js\n./dist/assets/vendor-react-BlSKgYtz.js\n./dist/assets/components-Bz12EpyG.js\n./dist/assets/polyfills-legacy-CVP5NLE8.js\n./dist/assets/vendor-ai-legacy-CyZNd2vD.js\n./dist/assets/vendor-syntax-DA_oj9j9.js\n./dist/assets/vendor-ai-CrZYw_n5.js\n./dist/assets/vendor-syntax-legacy-BjokNLxB.js\n./dist/assets/vendor-react-legacy-Bx_qFO1u.js\n./dist/assets/polyfills-7bmBDXKC.js\n./dist/assets/vendor-react-dom-DPrzmZd9.js\n./dist/assets/vendor-CMDDOKLY.js"}], "localResources": [{"type": "字体", "path": "public/assets/fonts", "count": 2}, {"type": "字体", "path": "public/assets/webfonts", "count": 6}, {"type": "静态资源", "path": "public/assets", "count": 8}, {"type": "静态资源", "path": "src/assets", "count": 1}, {"type": "npm依赖", "path": "package.json", "count": 39}, {"type": "Service Worker", "path": "public/sw.js", "count": 1}, {"type": "PWA Manifest", "path": "public/manifest.json", "count": 1}], "recommendations": ["运行内网优化脚本: node scripts/prepare-intranet-deployment.cjs"]}