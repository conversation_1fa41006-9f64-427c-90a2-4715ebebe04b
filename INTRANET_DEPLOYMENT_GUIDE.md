# YNNX AI 平台内网部署指南

## 📋 部署概述

YNNX AI 平台已完成内网部署优化，所有外部依赖已本地化，可在完全离线的内网环境中运行。

## ✅ 已完成的优化

### 1. 外部依赖消除
- ✅ 移除所有 Google Fonts 外部链接
- ✅ 本地化 Font Awesome 字体文件
- ✅ 替换 CopilotKit 外部 API 链接为内网地址
- ✅ 创建本地化文档和支持页面
- ✅ 移除 CDN 预连接链接

### 2. 资源本地化
- ✅ 字体文件：`dist/assets/webfonts/`
- ✅ 本地文档：`dist/local-resources/`
- ✅ Schema.org 上下文：`dist/local-resources/schema-org-context.json`
- ✅ CopilotKit 支持页面：完整的本地化帮助系统

### 3. 构建配置优化
- ✅ Vite 配置：外部模块处理
- ✅ 代码分割：优化加载性能
- ✅ 构建后处理：自动替换外部链接
- ✅ 验证脚本：全面的部署检查

## 🚀 部署步骤

### 步骤 1: 环境准备
```bash
# 克隆项目
git clone <repository-url>
cd ynnx-aidev-platform

# 安装依赖
npm install
```

### 步骤 2: 配置内网环境
```bash
# 复制内网配置文件
cp .env.intranet .env

# 编辑配置文件，修改为实际的内网地址
nano .env
```

### 步骤 3: 构建应用
```bash
# 执行内网构建
npm run build:intranet

# 或者使用标准构建 + 后处理
npm run build
```

### 步骤 4: 验证部署
```bash
# 运行验证脚本
npm run verify:intranet

# 查看详细报告
cat intranet-verification-report.json
```

### 步骤 5: 部署到服务器
```bash
# 复制构建产物到 Web 服务器
cp -r dist/* /var/www/html/

# 或使用 nginx 配置
sudo systemctl reload nginx
```

## ⚙️ 内网服务配置

### 必需的内网服务

1. **LiteLLM API 服务**
   - 地址：`http://your-internal-litellm-server:4000`
   - 用途：AI 模型代理服务

2. **LDAP 认证服务**
   - 地址：`ldap://your-ldap-server:389`
   - 用途：用户身份认证

3. **Web 服务器**
   - 推荐：Nginx 或 Apache
   - 用途：托管静态文件

### 可选的内网服务

1. **Redis 缓存**
   - 地址：`redis://your-internal-redis:6379`
   - 用途：会话和数据缓存

2. **PostgreSQL 数据库**
   - 地址：`************************************************/ynnx_ai`
   - 用途：应用数据存储

## 🔧 Nginx 配置示例

```nginx
server {
    listen 80;
    server_name your-intranet-domain.com;
    root /var/www/html;
    index index.html;

    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # SPA 路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }

    # API 代理
    location /api/ {
        proxy_pass http://your-internal-api-server:3000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    # LiteLLM 代理
    location /litellm/ {
        proxy_pass http://your-internal-litellm-server:4000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 🔍 验证清单

- [ ] 构建产物完整（17 项检查通过）
- [ ] 无有问题的外部链接
- [ ] 字体文件本地化完成
- [ ] 环境变量正确配置
- [ ] Service Worker 无外部依赖
- [ ] 依赖包完整安装
- [ ] 内网服务正常运行
- [ ] 用户认证功能正常
- [ ] AI 助手功能可用

## 📊 性能优化建议

### 当前状态
- 构建大小：454M（包含完整的语法高亮和 AI 功能）
- 通过项目：16/17
- 警告项目：1（构建大小）

### 优化建议
1. **代码分割优化**：按需加载语法高亮语言
2. **资源压缩**：启用 gzip/brotli 压缩
3. **CDN 配置**：使用内网 CDN 分发静态资源
4. **缓存策略**：配置适当的浏览器缓存

## 🛠️ 故障排除

### 常见问题

1. **AI 助手无响应**
   - 检查 LiteLLM 服务状态
   - 验证 API 配置
   - 查看浏览器控制台错误

2. **认证失败**
   - 检查 LDAP 服务连接
   - 验证用户凭据
   - 确认用户组权限

3. **页面加载缓慢**
   - 检查网络延迟
   - 启用资源压缩
   - 优化缓存配置

### 支持资源
- 本地文档：`/local-resources/copilotkit-info.html`
- 故障排除：`/local-resources/copilotkit-troubleshooting.html`
- 技术支持：`/local-resources/copilotkit-support.html`

## 📞 技术支持

如需技术支持，请联系：
- 内网管理员
- AI 平台技术团队
- 查看本地文档中心

---

**部署完成后，YNNX AI 平台将完全在内网环境中运行，无需任何外部网络连接。**
