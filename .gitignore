# ===== Node.js =====
# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Package manager files (keep package-lock.json for consistency)
# package-lock.json  # Keep this for npm projects
yarn.lock
pnpm-lock.yaml

# ===== Build outputs =====
# Vite
dist/
dist-ssr/
build/

# ===== Environment variables =====
# .env
# .env.local
# .env.development.local
# .env.test.local
# .env.production.local
# .env.*.local
# .env.development
# .env.production

# ===== Logs =====
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# ===== IDE/Editor =====
# VS Code
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json

# IntelliJ IDEA
.idea/
*.iml
*.ipr
*.iws

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc

# ===== Operating System =====
# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ===== Cache =====
# npm
.npm
.npmrc

# Yarn
.yarn-integrity
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# ESLint
.eslintcache

# StyleLint
.stylelintcache

# ===== Testing =====
# Coverage directory used by tools like istanbul
coverage/
*.lcov
.nyc_output

# Jest
jest_0

# Cypress
cypress/videos/
cypress/screenshots/

# ===== Development =====
# Hot-reload
*.local

# Temporary files
*.tmp
*.temp
*.swp
*.swo

# ===== Deployment =====
# Vercel
.vercel

# Netlify
.netlify

# Firebase
.firebase/
firebase-debug.log

# ===== Database =====
*.db
*.sqlite
*.sqlite3

# ===== Enterprise/Security =====
# API keys and secrets
secrets.json
config/secrets.*
.secret
.secrets/

# Certificate files
*.pem
*.key
*.crt
*.cert

# Backup files
*.backup
*.bak
*.old

# Documentation build
docs/_build/

# ===== Custom for YNNX AI Platform =====
# User uploads
uploads/
user-files/

# Generated AI models (if any)
models/
*.model

# Custom logs
logs/ynnx-*.log
audit-*.log

# Development database
dev.db
development.sqlite

# LDAP configuration (contains sensitive credentials)
# 已删除，现在使用环境变量配置
# ldap-config.json
ldap-config.*.json

# Custom config files (keep templates)
config/local.*
!config/local.example.*
