#!/usr/bin/env node

/**
 * 构建后处理脚本：替换外部链接为本地资源
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔗 开始替换外部链接...');

// 外部链接映射
const linkReplacements = {
  'https://cloud.copilotkit.ai': '/local-resources/copilotkit-cloud-info.html',
  'https://api.cloud.copilotkit.ai': 'http://localhost:4000', // 替换为内网 API 地址
  'https://copilotkit.ai/': '/local-resources/copilotkit-info.html',
  'https://docs.copilotkit.ai/coagents/troubleshooting/common-issues': '/local-resources/copilotkit-troubleshooting.html',
  'https://docs.copilotkit.ai/troubleshooting/common-issues': '/local-resources/copilotkit-troubleshooting.html',
  'https://docs.copilotkit.ai': '/local-resources/copilotkit-troubleshooting.html',
  'https://go.copilotkit.ai/dev-console-support-discord': '/local-resources/copilotkit-support.html',
  'https://go.copilotkit.ai/dev-console-support-slack': '/local-resources/copilotkit-support.html'
};

// 递归处理目录中的文件
function processDirectory(dirPath) {
  const files = fs.readdirSync(dirPath);
  
  files.forEach(file => {
    const filePath = path.join(dirPath, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      processDirectory(filePath);
    } else if (file.endsWith('.js') || file.endsWith('.css') || file.endsWith('.html')) {
      processFile(filePath);
    }
  });
}

// 处理单个文件
function processFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf-8');
    let modified = false;
    
    // 替换所有外部链接
    Object.entries(linkReplacements).forEach(([external, local]) => {
      const regex = new RegExp(external.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g');
      if (content.includes(external)) {
        content = content.replace(regex, local);
        modified = true;
        console.log(`✅ 替换链接: ${path.basename(filePath)} - ${external} -> ${local}`);
      }
    });
    
    // 如果文件被修改，写回文件
    if (modified) {
      fs.writeFileSync(filePath, content);
    }
  } catch (error) {
    console.warn(`⚠️ 处理文件失败: ${filePath} - ${error.message}`);
  }
}

// 主处理逻辑
if (fs.existsSync('dist')) {
  console.log('📂 处理 dist 目录...');
  processDirectory('dist');
  
  // 验证替换结果
  console.log('\n🔍 验证替换结果...');
  try {
    const result = execSync('find dist -type f \\( -name "*.js" -o -name "*.css" -o -name "*.html" \\) -exec grep -l "https://.*copilotkit" {} \\;', 
      { encoding: 'utf-8' });
    
    if (result.trim()) {
      console.log('⚠️ 仍有文件包含 CopilotKit 外部链接:');
      console.log(result);
    } else {
      console.log('✅ 所有 CopilotKit 外部链接已成功替换');
    }
  } catch (error) {
    console.log('✅ 所有 CopilotKit 外部链接已成功替换');
  }
  
  console.log('\n🎉 外部链接替换完成！');
} else {
  console.log('❌ dist 目录不存在，请先运行构建');
  process.exit(1);
}
