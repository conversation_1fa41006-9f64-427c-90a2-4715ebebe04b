<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试智能问题路由</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .download-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #007bff;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        .tool-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            margin: 10px 0;
            background: white;
            border-radius: 6px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .download-btn {
            background-color: #28a745;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }
        .download-btn:hover {
            background-color: #218838;
        }
        .test-controls {
            margin: 20px 0;
            padding: 20px;
            background-color: #e9ecef;
            border-radius: 8px;
        }
        .test-question {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 4px;
            cursor: pointer;
            border: 1px solid #ddd;
        }
        .test-question:hover {
            background-color: #f8f9fa;
        }
        .console-output {
            margin: 20px 0;
            padding: 15px;
            background-color: #1e1e1e;
            color: #00ff00;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 开发工具下载中心</h1>
        <p>这里提供各种开发工具和软件的下载服务</p>

        <div class="download-section">
            <h2>🛠️ 开发工具</h2>
            
            <div class="tool-item">
                <div>
                    <h3>Visual Studio Code</h3>
                    <p>轻量级代码编辑器，支持多种编程语言</p>
                    <small>版本: 1.85.0 | 平台: Windows, macOS, Linux</small>
                </div>
                <a href="#" class="download-btn">立即下载</a>
            </div>

            <div class="tool-item">
                <div>
                    <h3>Git for Windows</h3>
                    <p>Windows平台的Git版本控制工具</p>
                    <small>版本: 2.43.0 | 平台: Windows</small>
                </div>
                <a href="#" class="download-btn">立即下载</a>
            </div>

            <div class="tool-item">
                <div>
                    <h3>Node.js</h3>
                    <p>JavaScript运行时环境</p>
                    <small>版本: 20.10.0 LTS | 平台: 全平台</small>
                </div>
                <a href="#" class="download-btn">立即下载</a>
            </div>

            <div class="tool-item">
                <div>
                    <h3>Docker Desktop</h3>
                    <p>容器化应用开发平台</p>
                    <small>版本: 4.26.0 | 平台: Windows, macOS</small>
                </div>
                <a href="#" class="download-btn">立即下载</a>
            </div>
        </div>

        <div class="download-section">
            <h2>📚 开发文档</h2>
            
            <div class="tool-item">
                <div>
                    <h3>API 开发指南</h3>
                    <p>完整的API开发文档和示例</p>
                    <small>格式: PDF | 大小: 2.5MB</small>
                </div>
                <a href="#" class="download-btn">下载PDF</a>
            </div>

            <div class="tool-item">
                <div>
                    <h3>最佳实践手册</h3>
                    <p>编程最佳实践和代码规范</p>
                    <small>格式: PDF | 大小: 1.8MB</small>
                </div>
                <a href="#" class="download-btn">下载PDF</a>
            </div>
        </div>

        <div class="test-controls">
            <h3>🧪 测试智能问题路由</h3>
            <p>点击下面的问题来测试AI助手的智能路由功能：</p>
            
            <div class="test-question" onclick="testQuestion('工具下载有什么')">
                ❓ 工具下载有什么
            </div>
            
            <div class="test-question" onclick="testQuestion('页面上有哪些下载内容')">
                ❓ 页面上有哪些下载内容
            </div>
            
            <div class="test-question" onclick="testQuestion('这里显示了什么工具')">
                ❓ 这里显示了什么工具
            </div>
            
            <div class="test-question" onclick="testQuestion('如何安装Node.js')">
                ❓ 如何安装Node.js
            </div>
            
            <div class="test-question" onclick="testQuestion('Git使用教程')">
                ❓ Git使用教程
            </div>
        </div>

        <div class="console-output" id="consoleOutput">
            <div>🔍 控制台输出将显示在这里...</div>
            <div>请打开浏览器开发者工具查看详细日志</div>
        </div>
    </div>

    <script>
        // 捕获控制台输出
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        const consoleOutput = document.getElementById('consoleOutput');

        function addToConsole(message, type = 'log') {
            const div = document.createElement('div');
            div.style.color = type === 'error' ? '#ff6b6b' : type === 'warn' ? '#ffd93d' : '#00ff00';
            div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            consoleOutput.appendChild(div);
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }

        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };

        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };

        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToConsole(args.join(' '), 'warn');
        };

        // 测试问题函数
        function testQuestion(question) {
            console.log(`\n=== 开始测试问题: "${question}" ===`);

            // 检查是否在主应用中
            if (window.parent !== window) {
                console.log('在iframe中，尝试与父窗口通信...');
                window.parent.postMessage({
                    type: 'TEST_SMART_ROUTER',
                    question: question
                }, '*');
                return;
            }

            // 检查全局对象
            console.log('检查全局对象:');
            console.log('- window.copilotKit:', !!window.copilotKit);
            console.log('- window.pageContentCollector:', !!window.pageContentCollector);
            console.log('- window.enhancedDocumentService:', !!window.enhancedDocumentService);

            // 尝试直接调用页面内容收集器
            if (window.pageContentCollector) {
                console.log('找到页面内容收集器，开始测试...');
                try {
                    const content = window.pageContentCollector.collectPageContent();
                    console.log('页面内容收集结果:', content);
                } catch (error) {
                    console.error('页面内容收集失败:', error);
                }
            }

            // 模拟调用智能问题路由
            if (window.copilotKit && window.copilotKit.actions) {
                const smartRouter = window.copilotKit.actions.find(action => action.name === 'smartQuestionRouter');
                if (smartRouter) {
                    console.log('找到智能问题路由Action，开始调用...');
                    smartRouter.handler({ question: question })
                        .then(result => {
                            console.log('智能路由结果:', result);
                        })
                        .catch(error => {
                            console.error('智能路由失败:', error);
                        });
                } else {
                    console.warn('未找到智能问题路由Action');
                    console.log('可用的Actions:', window.copilotKit.actions?.map(a => a.name));
                }
            } else {
                console.warn('CopilotKit未初始化');

                // 尝试手动测试问题分析逻辑
                console.log('手动测试问题分析逻辑...');
                testQuestionAnalysis(question);
            }
        }

        // 手动测试问题分析逻辑
        function testQuestionAnalysis(question) {
            console.log('=== 手动问题分析测试 ===');
            const q = question.toLowerCase();
            console.log('问题转小写:', q);

            // 页面内容相关的关键词
            const pageContentKeywords = [
                '页面', '当前', '这里', '显示', '展示', '界面', '屏幕',
                '工具下载', '下载', '有什么', '包含', '列出', '看到',
                '页面上', '这个页面', '当前页面', '界面上'
            ];

            let pageScore = 0;
            const pageMatches = [];

            pageContentKeywords.forEach(keyword => {
                if (q.includes(keyword)) {
                    pageScore += 2;
                    pageMatches.push(keyword);
                }
            });

            // 特殊规则
            if (q.includes('工具下载有什么')) {
                pageScore += 5;
                pageMatches.push('工具下载有什么(特殊规则)');
            }

            console.log('页面内容关键词匹配:', pageMatches);
            console.log('页面内容得分:', pageScore);

            if (pageScore > 0) {
                console.log('✅ 应该使用页面分析模式');
                testPageContentCollection();
            } else {
                console.log('❌ 不会触发页面分析模式');
            }
        }

        // 测试页面内容收集
        function testPageContentCollection() {
            console.log('=== 测试页面内容收集 ===');

            // 查找下载相关元素
            const downloadBtns = document.querySelectorAll('.download-btn, [class*="download"], [href*="download"]');
            console.log('找到下载按钮:', downloadBtns.length);

            const toolItems = document.querySelectorAll('.tool-item, [class*="tool"]');
            console.log('找到工具项目:', toolItems.length);

            const downloadSections = document.querySelectorAll('.download-section, [class*="download"]');
            console.log('找到下载区域:', downloadSections.length);

            // 模拟下载内容分析
            const mockDownloadContent = {
                tools: [],
                downloadButtons: [],
                downloadLinks: [],
                totalDownloads: 0,
                hasDownloadSection: downloadSections.length > 0
            };

            toolItems.forEach((item, index) => {
                const title = item.querySelector('h3')?.textContent || `工具${index + 1}`;
                const description = item.querySelector('p')?.textContent || '';
                const version = item.querySelector('small')?.textContent || '';

                mockDownloadContent.tools.push({
                    name: title,
                    description: description,
                    version: version
                });
            });

            downloadBtns.forEach((btn, index) => {
                mockDownloadContent.downloadButtons.push({
                    text: btn.textContent || `下载按钮${index + 1}`,
                    disabled: btn.disabled || false
                });
            });

            mockDownloadContent.totalDownloads = mockDownloadContent.tools.length;

            console.log('模拟下载内容分析结果:', mockDownloadContent);

            // 模拟格式化答案
            const mockAnswer = {
                answerType: 'download-focused',
                downloadSummary: mockDownloadContent,
                pageContext: {
                    title: document.title,
                    url: window.location.href
                }
            };

            console.log('模拟格式化答案:', mockAnswer);
            console.log('✅ 页面内容收集测试完成');
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 调试页面加载完成');
            console.log('页面包含以下下载内容:');
            
            const downloadBtns = document.querySelectorAll('.download-btn');
            console.log(`- 下载按钮: ${downloadBtns.length} 个`);
            
            const toolItems = document.querySelectorAll('.tool-item');
            console.log(`- 工具项目: ${toolItems.length} 个`);
            
            toolItems.forEach((item, index) => {
                const title = item.querySelector('h3')?.textContent;
                console.log(`  ${index + 1}. ${title}`);
            });
            
            console.log('\n请点击测试问题来验证智能路由功能');
        });

        // 监听性能警告
        const originalSetTimeout = window.setTimeout;
        window.setTimeout = function(callback, delay) {
            const start = performance.now();
            return originalSetTimeout(() => {
                const duration = performance.now() - start;
                if (duration > 200) {
                    console.warn(`⚠️ setTimeout执行时间过长: ${duration.toFixed(2)}ms`);
                }
                callback();
            }, delay);
        };
    </script>
</body>
</html>
