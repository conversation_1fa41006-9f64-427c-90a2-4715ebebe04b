# 使用国内源的Node.js镜像
FROM node:22-alpine AS base

# 设置工作目录
WORKDIR /app

# 无需安装额外工具，使用Node.js做健康检查

# 配置npm使用国内镜像
RUN npm config set registry https://registry.npmmirror.com/

# 复制依赖文件
COPY package*.json ./

# 安装全部依赖（构建需要）
RUN npm ci --silent

# 设置构建时环境变量
ARG VITE_LDAP_API_URL=http://localhost:3002
ARG VITE_LITELLM_API_BASE=http://localhost:4000
ENV VITE_LDAP_API_URL=$VITE_LDAP_API_URL
ENV VITE_LITELLM_API_BASE=$VITE_LITELLM_API_BASE

# 复制源代码
COPY . .

# 重新构建前端（在容器中）
RUN npm run build

# 暴露端口
EXPOSE 3002

# 创建启动脚本
RUN echo '#!/bin/sh' > /app/start.sh && \
    echo 'node src/server/ldapAuthServer.js &' >> /app/start.sh && \
    echo 'wait' >> /app/start.sh && \
    chmod +x /app/start.sh

# 启动应用
CMD ["/app/start.sh"] 