# YNNX AI Platform 部署指南

## 🚀 生产环境部署完整方案

基于硬编码配置修复后的完整部署流程。

## 📋 部署前检查清单

### 1. 环境准备
- [ ] Node.js >= 18.0.0
- [ ] npm >= 9.0.0
- [ ] PM2 进程管理器
- [ ] Nginx 反向代理
- [ ] SSL 证书

### 2. 配置检查
```bash
# 验证环境配置
npm run config:validate

# 检查硬编码问题
npm run config:check

# 代码质量检查
npm run lint
```

## 🔧 生产环境配置

### 1. 环境变量设置

创建生产环境配置文件 `.env.production`:

```bash
# ===== 应用基础配置 =====
NODE_ENV=production
CORS_ORIGIN=https://ai.ynnx.com

# ===== 前端API配置 =====
VITE_LDAP_API_URL=https://ai-ldap.ynnx.com
VITE_CHAT_API_URL=https://ai-api.ynnx.com
VITE_LITELLM_API_BASE=https://ai-llm.ynnx.com

# ===== LDAP 生产配置 =====
LDAP_240.10云桌面环境_URL=ldap://ldap-prod-240.ynnx.com:389
LDAP_240.10云桌面环境_BIND_PASSWORD=生产环境密码
LDAP_242.2云桌面环境_URL=ldap://ldap-prod-242.ynnx.com:389
LDAP_242.2云桌面环境_BIND_PASSWORD=生产环境密码

# ===== LLM 生产配置 =====
LITELLM_MASTER_KEY=生产环境主密钥
OPENAI_API_KEY=生产环境OpenAI密钥
ANTHROPIC_API_KEY=生产环境Anthropic密钥

# ===== 性能配置 =====
LLM_TIMEOUT=60000
API_RETRY_COUNT=3
```

### 2. 安全配置

#### 密钥管理
```bash
# 使用密钥管理系统
export OPENAI_API_KEY=$(vault kv get -field=key secret/ai-platform/openai)
export ANTHROPIC_API_KEY=$(vault kv get -field=key secret/ai-platform/anthropic)
export LITELLM_MASTER_KEY=$(vault kv get -field=key secret/ai-platform/litellm)
```

#### 防火墙规则
```bash
# 基础端口开放
ufw allow 80/tcp
ufw allow 443/tcp

# 内网API访问
# 3001端口已不再使用（NLWEB API已移除）
ufw allow from 192.168.0.0/16 to any port 3002
```

## 🏗️ 构建与部署

### 1. 构建流程

```bash
# 1. 安装依赖
npm ci --only=production

# 2. 配置验证
npm run config:validate

# 3. 构建生产版本
npm run build

# 构建过程包含：
# - UI内容索引构建
# - Vite生产环境优化
# - 关键路径优化
# - 静态资源压缩
```

### 2. 构建优化特性

#### 代码分割策略
- **React核心**: `react-vendor` chunk
- **路由**: `router` chunk  
- **动画**: `animation` chunk
- **图标**: `icons` chunk
- **AI SDK**: `ai-sdk` chunk

#### 性能优化
- 支持Chrome 60+, Firefox 60+, Safari 12+
- 自动生成Legacy版本
- 移除生产环境console日志
- 静态资源CDN优化

### 3. 部署架构

```
                [负载均衡器/Nginx]
                       |
          +------------+------------+
          |                         |
     [前端服务器]              [API服务器集群]
    (静态文件服务)               |
                                |
                       +--------+--------+
                       |        |        |
                 [LDAP API] [LiteLLM]
:3002      :4000
```

## 🌐 Nginx 配置

### 前端静态文件服务

```nginx
server {
    listen 80;
    listen 443 ssl;
    server_name ai.ynnx.com;
    
    # SSL配置
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/private.key;
    
    # 静态文件服务
    location / {
        root /var/www/ynnx-ai-platform/dist;
        try_files $uri $uri/ /index.html;
        
        # 静态资源缓存
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # API代理
    location /api/ {
        proxy_pass http://api-servers;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}

# API服务器池
upstream api-servers {
    # NLWEB API服务器已移除
    keepalive 32;
}
```

## 🔄 PM2 进程管理

### PM2 配置文件 `ecosystem.config.js`

```javascript
module.exports = {
  apps: [
    // NLWEB API配置已移除
    {
      name: 'ynnx-ldap-auth',
      script: 'src/server/ldapAuthServer.js',
      instances: 2,
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'production',
        PORT: 3002
      },
      log_file: '/var/log/ynnx-ai/ldap.log',
      error_file: '/var/log/ynnx-ai/ldap-error.log',
      max_memory_restart: '300M'
    }
  ]
};
```

### PM2 操作命令

```bash
# 启动服务
pm2 start ecosystem.config.js --env production

# 查看状态
pm2 status

# 查看日志
pm2 logs

# 重启服务
pm2 restart all

# 停止服务
pm2 stop all
```

## 📊 监控与健康检查

### 健康检查脚本

```bash
#!/bin/bash
# health-check.sh

echo "🩺 YNNX AI Platform 健康检查..."

# NLWEB API健康检查已移除

# 检查LDAP服务
if curl -f http://localhost:3002/api/ldap/environments; then
    echo "✅ LDAP API 正常"
else
    echo "❌ LDAP API 异常"
    exit 1
fi

echo "🎉 所有服务运行正常"
```

### 监控指标

- API响应时间
- 错误率统计
- 内存和CPU使用率
- LDAP连接状态
- LLM API调用统计

## 🚀 一键部署脚本

```bash
#!/bin/bash
# deploy.sh - YNNX AI Platform 生产环境部署脚本

set -e

echo "🚀 开始 YNNX AI Platform 生产环境部署..."

# 1. 环境检查
echo "🔍 检查部署环境..."
node --version
npm --version

# 2. 安装依赖
echo "📦 安装生产依赖..."
npm ci --only=production

# 3. 配置验证
echo "🔧 验证配置..."
npm run config:validate

# 4. 硬编码检查
echo "🔍 检查硬编码问题..."
npm run config:check

# 5. 构建项目
echo "🏗️ 构建生产版本..."
npm run build

# 6. 部署静态文件
echo "📂 部署静态文件..."
rsync -av --delete dist/ /var/www/ynnx-ai-platform/

# 7. 启动服务
echo "🔄 启动服务..."
pm2 start ecosystem.config.js --env production

# 8. 健康检查
echo "🩺 执行健康检查..."
sleep 10
./health-check.sh

echo "✅ 部署完成！"
echo "🌐 访问地址: https://ai.ynnx.com"
```

## 🔧 故障排除

### 常见问题

1. **API连接失败**
   - 检查环境变量配置
   - 验证服务器防火墙规则
   - 查看PM2日志

2. **LDAP认证失败**
   - 验证LDAP服务器连通性
   - 检查认证凭据
   - 查看LDAP服务日志

3. **静态文件404**
   - 检查Nginx配置
   - 验证文件权限
   - 查看构建输出

### 日志查看

```bash
# PM2 日志
# NLWEB API日志已移除
pm2 logs ynnx-ldap-auth

# Nginx 日志
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log

# 系统日志
journalctl -u nginx -f
```

## 📝 部署后验证

- [ ] 前端页面正常加载
- [ ] LDAP认证功能正常
- [ ] AI聊天功能正常
- [ ] API密钥管理正常
- [ ] 下载功能正常
- [ ] 所有API接口响应正常
- [ ] 日志记录正常
- [ ] 性能指标正常

## 🔄 持续部署

### 自动化部署流程

1. 代码推送到主分支
2. CI/CD管道触发
3. 自动运行测试
4. 构建生产版本
5. 部署到预发布环境
6. 自动化测试验证
7. 部署到生产环境
8. 健康检查和监控

---

**注意**: 生产环境部署前请务必：
1. 完成所有硬编码配置修复
2. 设置正确的环境变量
3. 配置SSL证书
4. 设置监控和告警
5. 准备回滚方案 