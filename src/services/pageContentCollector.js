/**
 * 页面内容收集器 - 收集当前页面的所有可见内容和状态信息
 * 为AI提供完整的页面上下文感知能力
 */

class PageContentCollector {
  constructor() {
    this.contentCache = new Map();
    this.observers = new Set();
    this.lastUpdate = null;
    this.updateInterval = null;
    this.isCollecting = false;
    this.userInteractionTracker = new Map();
    this.errorLog = [];
    this.initializationPromise = null;
    this.isInitialized = false;

    // 延迟初始化，等待更合适的时机
    this.delayedInitialize();
  }

  /**
   * 延迟初始化，等待更合适的时机
   */
  delayedInitialize() {
    // 等待DOM完全加载和React组件挂载
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        // DOM加载完成后再等待一段时间，确保React组件挂载
        setTimeout(() => this.initializeCollection(), 500);
      });
    } else {
      // DOM已加载，等待React组件挂载
      setTimeout(() => this.initializeCollection(), 200);
    }
  }

  /**
   * 初始化内容收集
   */
  initializeCollection() {
    if (this.isInitialized) return;

    try {
      this.startCollection();
      this.isInitialized = true;
      console.log('[PageContentCollector] 初始化完成');
    } catch (error) {
      console.error('[PageContentCollector] 初始化失败:', error);
    }
  }

  /**
   * 开始收集页面内容
   */
  startCollection() {
    if (this.isCollecting) return;

    this.isCollecting = true;

    // 立即收集一次
    this.collectAllContent();

    // 设置定期更新，降低频率以提升性能
    this.updateInterval = setInterval(() => {
      // 只有在页面有变化时才收集
      if (this.shouldUpdateContent()) {
        this.collectAllContent();
      }
    }, 10000); // 改为每10秒更新一次

    // 监听页面变化
    this.setupObservers();

    // 设置用户交互跟踪
    this.setupUserInteractionTracking();

    // 设置错误日志收集
    this.setupErrorLogging();

    console.log('[PageContentCollector] 开始收集页面内容');
  }

  /**
   * 检查是否需要更新内容
   */
  shouldUpdateContent() {
    // 简单的启发式检查：如果页面URL或标题发生变化
    const currentUrl = window.location.href;
    const currentTitle = document.title;

    if (!this.lastUpdate) return true;

    const lastContent = this.getCurrentContent();
    if (!lastContent) return true;

    return lastContent.url !== currentUrl || lastContent.title !== currentTitle;
  }

  /**
   * 公共方法：收集页面内容（供外部调用）
   */
  async collectPageContent() {
    if (!this.isInitialized) {
      await this.ensureInitialized();
    }
    return this.collectAllContent();
  }

  /**
   * 确保已初始化
   */
  async ensureInitialized() {
    if (this.isInitialized) return;

    if (!this.initializationPromise) {
      this.initializationPromise = new Promise((resolve) => {
        this.initializeCollection();
        // 等待一小段时间确保初始化完成
        setTimeout(resolve, 100);
      });
    }

    await this.initializationPromise;
  }

  /**
   * 收集所有页面内容
   */
  collectAllContent() {
    try {
      const content = {
        timestamp: new Date().toISOString(),
        url: window.location.href,
        title: document.title,
        
        // 页面结构信息
        structure: this.collectPageStructure(),
        
        // 可见内容
        visibleContent: this.collectVisibleContent(),
        
        // 交互元素
        interactiveElements: this.collectInteractiveElements(),
        
        // 应用状态
        appState: this.collectAppState(),
        
        // 导航信息
        navigation: this.collectNavigationInfo(),
        
        // 媒体内容
        media: this.collectMediaContent(),
        
        // 性能信息
        performance: this.collectPerformanceInfo(),
        
        // 用户交互数据
        userInteraction: this.getUserInteractionSummary(),
        
        // 错误信息
        errors: this.getRecentErrors()
      };
      
      this.contentCache.set('current', content);
      this.lastUpdate = Date.now();
      
      // 触发更新事件
      this.dispatchUpdateEvent(content);
      
      return content;
    } catch (error) {
      console.error('页面内容收集失败:', error);
      this.logError('content_collection_failed', error);
      return null;
    }
  }

  /**
   * 收集页面结构信息
   */
  collectPageStructure() {
    const structure = {
      sections: [],
      landmarks: [],
      headings: []
    };

    // 收集页面区域
    const sections = document.querySelectorAll('section, div[id], main, article, aside, nav, header, footer');
    sections.forEach(section => {
      if (this.isElementVisible(section)) {
        structure.sections.push({
          id: section.id || null,
          tagName: section.tagName.toLowerCase(),
          className: section.className || null,
          textContent: section.textContent?.substring(0, 200) || '',
          position: this.getElementPosition(section)
        });
      }
    });

    // 收集地标元素
    const landmarks = document.querySelectorAll('[role], [aria-label], [aria-labelledby]');
    landmarks.forEach(landmark => {
      if (this.isElementVisible(landmark)) {
        structure.landmarks.push({
          role: landmark.getAttribute('role') || null,
          ariaLabel: landmark.getAttribute('aria-label') || null,
          ariaLabelledBy: landmark.getAttribute('aria-labelledby') || null,
          textContent: landmark.textContent?.substring(0, 100) || ''
        });
      }
    });

    // 收集标题
    const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
    headings.forEach(heading => {
      if (this.isElementVisible(heading)) {
        structure.headings.push({
          level: parseInt(heading.tagName.charAt(1)),
          text: heading.textContent?.trim() || '',
          id: heading.id || null
        });
      }
    });

    return structure;
  }

  /**
   * 收集可见内容
   */
  collectVisibleContent() {
    const content = {
      text: [],
      images: [],
      videos: [],
      lists: [],
      tables: [],
      codeBlocks: []
    };

    // 收集文本内容
    const textElements = document.querySelectorAll('p, span, div, article, section');
    textElements.forEach(el => {
      if (this.isElementVisible(el) && this.hasSignificantText(el)) {
        content.text.push({
          text: el.textContent?.trim().substring(0, 500) || '',
          tag: el.tagName.toLowerCase(),
          className: el.className || null,
          id: el.id || null
        });
      }
    });

    // 收集图片
    const images = document.querySelectorAll('img');
    images.forEach(img => {
      if (this.isElementVisible(img)) {
        content.images.push({
          src: img.src,
          alt: img.alt || '',
          title: img.title || '',
          width: img.width,
          height: img.height
        });
      }
    });

    // 收集视频
    const videos = document.querySelectorAll('video');
    videos.forEach(video => {
      if (this.isElementVisible(video)) {
        content.videos.push({
          src: video.src || video.currentSrc,
          poster: video.poster || '',
          duration: video.duration || 0,
          controls: video.controls
        });
      }
    });

    // 收集列表
    const lists = document.querySelectorAll('ul, ol');
    lists.forEach(list => {
      if (this.isElementVisible(list)) {
        const items = Array.from(list.querySelectorAll('li')).map(li => li.textContent?.trim() || '');
        content.lists.push({
          type: list.tagName.toLowerCase(),
          items: items.slice(0, 10), // 限制列表项数量
          className: list.className || null
        });
      }
    });

    // 收集表格
    const tables = document.querySelectorAll('table');
    tables.forEach(table => {
      if (this.isElementVisible(table)) {
        const headers = Array.from(table.querySelectorAll('th')).map(th => th.textContent?.trim() || '');
        const rows = Array.from(table.querySelectorAll('tr')).slice(1, 6).map(row => {
          return Array.from(row.querySelectorAll('td')).map(td => td.textContent?.trim() || '');
        });
        content.tables.push({
          headers,
          rows,
          className: table.className || null
        });
      }
    });

    // 收集代码块
    const codeBlocks = document.querySelectorAll('code, pre');
    codeBlocks.forEach(code => {
      if (this.isElementVisible(code)) {
        content.codeBlocks.push({
          code: code.textContent?.substring(0, 300) || '',
          language: code.className?.match(/language-(\w+)/)?.[1] || 'text',
          tag: code.tagName.toLowerCase()
        });
      }
    });

    return content;
  }

  /**
   * 收集交互元素
   */
  collectInteractiveElements() {
    const elements = {
      buttons: [],
      links: [],
      forms: [],
      inputs: []
    };

    // 收集按钮
    const buttons = document.querySelectorAll('button, input[type="button"], input[type="submit"], [role="button"]');
    buttons.forEach(button => {
      if (this.isElementVisible(button)) {
        elements.buttons.push({
          text: button.textContent?.trim() || button.value || '',
          type: button.type || 'button',
          disabled: button.disabled || false,
          className: button.className || null,
          id: button.id || null
        });
      }
    });

    // 收集链接
    const links = document.querySelectorAll('a[href]');
    links.forEach(link => {
      if (this.isElementVisible(link)) {
        elements.links.push({
          text: link.textContent?.trim() || '',
          href: link.href,
          target: link.target || '_self',
          className: link.className || null
        });
      }
    });

    // 收集表单
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
      if (this.isElementVisible(form)) {
        elements.forms.push({
          action: form.action || '',
          method: form.method || 'GET',
          inputs: Array.from(form.querySelectorAll('input, select, textarea')).map(input => ({
            name: input.name || '',
            type: input.type || 'text',
            placeholder: input.placeholder || '',
            required: input.required || false
          }))
        });
      }
    });

    // 收集输入框
    const inputs = document.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
      if (this.isElementVisible(input)) {
        elements.inputs.push({
          name: input.name || '',
          type: input.type || 'text',
          placeholder: input.placeholder || '',
          value: input.type === 'password' ? '[PASSWORD]' : (input.value || ''),
          required: input.required || false,
          disabled: input.disabled || false
        });
      }
    });

    return elements;
  }

  /**
   * 收集应用状态（增强版）
   */
  collectAppState() {
    const state = {
      route: {
        path: window.location.pathname,
        search: window.location.search,
        hash: window.location.hash
      },
      user: null,
      theme: null,
      loading: false,
      modals: [],
      notifications: [],

      // 新增：React应用状态检测
      reactState: this.detectReactState(),

      // 新增：功能区域识别
      functionalAreas: this.identifyFunctionalAreas(),

      // 新增：用户工作流状态
      workflowState: this.analyzeWorkflowState(),

      // 新增：页面可用性分析
      accessibility: this.analyzeAccessibility(),

      // 新增：下载内容分析
      downloadContent: this.analyzeDownloadContent()
    };

    // 检测用户状态（增强）
    const userIndicator = document.querySelector('[data-user-status], .user-info, .login-info, .user-avatar, .profile-menu');
    if (userIndicator) {
      state.user = {
        loggedIn: !userIndicator.textContent?.includes('登录'),
        username: this.extractUsernameFromElement(userIndicator),
        avatar: this.extractUserAvatar(userIndicator),
        role: this.detectUserRole()
      };
    }

    // 检测主题
    const themeIndicators = document.querySelectorAll('[data-theme], .theme-toggle, .dark-mode');
    themeIndicators.forEach(indicator => {
      if (indicator.textContent?.includes('暗') || indicator.classList.contains('dark')) {
        state.theme = 'dark';
      } else if (indicator.textContent?.includes('亮') || indicator.classList.contains('light')) {
        state.theme = 'light';
      }
    });

    // 检测加载状态
    const loadingIndicators = document.querySelectorAll('.loading, .spinner, [data-loading]');
    state.loading = loadingIndicators.length > 0;

    // 检测模态框
    const modals = document.querySelectorAll('.modal, .dialog, [role="dialog"]');
    modals.forEach(modal => {
      if (this.isElementVisible(modal)) {
        state.modals.push({
          title: modal.querySelector('.modal-title, .dialog-title')?.textContent?.trim() || '',
          content: modal.textContent?.substring(0, 200) || '',
          className: modal.className || null
        });
      }
    });

    // 检测通知
    const notifications = document.querySelectorAll('.notification, .alert, .toast, [role="alert"]');
    notifications.forEach(notification => {
      if (this.isElementVisible(notification)) {
        state.notifications.push({
          type: this.getNotificationType(notification),
          message: notification.textContent?.trim() || '',
          className: notification.className || null
        });
      }
    });

    return state;
  }

  /**
   * 收集导航信息
   */
  collectNavigationInfo() {
    const navigation = {
      menu: [],
      breadcrumbs: [],
      pagination: null,
      tabs: []
    };

    // 收集菜单
    const menus = document.querySelectorAll('nav, .nav, .menu, [role="navigation"]');
    menus.forEach(menu => {
      if (this.isElementVisible(menu)) {
        const items = Array.from(menu.querySelectorAll('a, button, .nav-item')).map(item => ({
          text: item.textContent?.trim() || '',
          href: item.href || null,
          active: item.classList.contains('active') || item.getAttribute('aria-current') === 'page'
        }));
        navigation.menu.push({
          items: items.slice(0, 20), // 限制菜单项数量
          className: menu.className || null
        });
      }
    });

    // 收集面包屑
    const breadcrumbs = document.querySelectorAll('.breadcrumb, .breadcrumbs, [aria-label*="面包屑"]');
    breadcrumbs.forEach(breadcrumb => {
      if (this.isElementVisible(breadcrumb)) {
        const items = Array.from(breadcrumb.querySelectorAll('a, span, .breadcrumb-item')).map(item => ({
          text: item.textContent?.trim() || '',
          href: item.href || null
        }));
        navigation.breadcrumbs.push(items);
      }
    });

    // 收集分页
    const pagination = document.querySelector('.pagination, .pager, [aria-label*="分页"]');
    if (pagination && this.isElementVisible(pagination)) {
      navigation.pagination = {
        current: pagination.querySelector('.current, .active')?.textContent?.trim() || '',
        total: pagination.querySelectorAll('a, button').length,
        hasNext: !!pagination.querySelector('.next, [aria-label*="下一"]'),
        hasPrev: !!pagination.querySelector('.prev, .previous, [aria-label*="上一"]')
      };
    }

    // 收集标签页
    const tabs = document.querySelectorAll('.tabs, .tab-list, [role="tablist"]');
    tabs.forEach(tabList => {
      if (this.isElementVisible(tabList)) {
        const items = Array.from(tabList.querySelectorAll('[role="tab"], .tab')).map(tab => ({
          text: tab.textContent?.trim() || '',
          active: tab.getAttribute('aria-selected') === 'true' || tab.classList.contains('active'),
          disabled: tab.getAttribute('aria-disabled') === 'true' || tab.classList.contains('disabled')
        }));
        navigation.tabs.push(items);
      }
    });

    return navigation;
  }

  /**
   * 收集媒体内容
   */
  collectMediaContent() {
    const media = {
      totalImages: 0,
      totalVideos: 0,
      totalAudios: 0,
      imageTypes: new Set(),
      videoTypes: new Set(),
      audioTypes: new Set()
    };

    // 统计图片
    const images = document.querySelectorAll('img');
    media.totalImages = images.length;
    images.forEach(img => {
      const type = img.src.split('.').pop()?.toLowerCase();
      if (type) media.imageTypes.add(type);
    });

    // 统计视频
    const videos = document.querySelectorAll('video');
    media.totalVideos = videos.length;
    videos.forEach(video => {
      const type = video.src?.split('.').pop()?.toLowerCase();
      if (type) media.videoTypes.add(type);
    });

    // 统计音频
    const audios = document.querySelectorAll('audio');
    media.totalAudios = audios.length;
    audios.forEach(audio => {
      const type = audio.src?.split('.').pop()?.toLowerCase();
      if (type) media.audioTypes.add(type);
    });

    return {
      totalImages: media.totalImages,
      totalVideos: media.totalVideos,
      totalAudios: media.totalAudios,
      imageTypes: Array.from(media.imageTypes),
      videoTypes: Array.from(media.videoTypes),
      audioTypes: Array.from(media.audioTypes)
    };
  }

  /**
   * 收集性能信息
   */
  collectPerformanceInfo() {
    const performance = {
      loadTime: null,
      domElements: document.querySelectorAll('*').length,
      scripts: document.querySelectorAll('script').length,
      stylesheets: document.querySelectorAll('link[rel="stylesheet"]').length,
      memoryUsage: null,
      networkRequests: null
    };

    // 获取加载时间
    if (window.performance && window.performance.timing) {
      const timing = window.performance.timing;
      performance.loadTime = timing.loadEventEnd - timing.navigationStart;
    }

    // 获取内存使用情况
    if (window.performance && window.performance.memory) {
      performance.memoryUsage = {
        used: window.performance.memory.usedJSHeapSize,
        total: window.performance.memory.totalJSHeapSize,
        limit: window.performance.memory.jsHeapSizeLimit
      };
    }

    // 获取网络请求数量
    if (window.performance && window.performance.getEntriesByType) {
      const resources = window.performance.getEntriesByType('resource');
      performance.networkRequests = resources.length;
    }

    return performance;
  }

  /**
   * 设置页面变化监听器
   */
  setupObservers() {
    // DOM 变化监听器
    const observer = new MutationObserver((mutations) => {
      let shouldUpdate = false;
      
      mutations.forEach(mutation => {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          shouldUpdate = true;
        }
        if (mutation.type === 'attributes' && 
            ['class', 'style', 'hidden', 'aria-hidden'].includes(mutation.attributeName)) {
          shouldUpdate = true;
        }
      });
      
      if (shouldUpdate) {
        // 防抖更新
        clearTimeout(this.debounceTimer);
        this.debounceTimer = setTimeout(() => {
          this.collectAllContent();
        }, 1000);
      }
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['class', 'style', 'hidden', 'aria-hidden']
    });

    this.observers.add(observer);

    // 视口变化监听器
    const resizeObserver = new ResizeObserver(() => {
      this.collectAllContent();
    });

    resizeObserver.observe(document.body);
    this.observers.add(resizeObserver);
  }

  /**
   * 设置用户交互跟踪
   */
  setupUserInteractionTracking() {
    const events = ['click', 'scroll', 'focus', 'blur', 'input'];
    
    events.forEach(eventType => {
      document.addEventListener(eventType, (event) => {
        const timestamp = Date.now();
        const target = event.target;
        
        const interaction = {
          type: eventType,
          timestamp,
          element: {
            tagName: target.tagName,
            className: target.className || null,
            id: target.id || null,
            textContent: target.textContent?.substring(0, 100) || ''
          }
        };

        // 只保留最近的交互记录
        if (!this.userInteractionTracker.has(eventType)) {
          this.userInteractionTracker.set(eventType, []);
        }
        
        const interactions = this.userInteractionTracker.get(eventType);
        interactions.push(interaction);
        
        // 只保留最近20个交互
        if (interactions.length > 20) {
          interactions.shift();
        }
      });
    });
  }

  /**
   * 设置错误日志收集
   */
  setupErrorLogging() {
    // JavaScript错误
    window.addEventListener('error', (event) => {
      this.logError('javascript_error', {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        error: event.error?.toString()
      });
    });

    // 网络错误
    window.addEventListener('unhandledrejection', (event) => {
      this.logError('promise_rejection', {
        reason: event.reason?.toString()
      });
    });

    // 控制台错误
    const originalConsoleError = console.error;
    console.error = (...args) => {
      this.logError('console_error', {
        message: args.map(arg => arg?.toString()).join(' ')
      });
      originalConsoleError.apply(console, args);
    };
  }

  /**
   * 记录错误
   */
  logError(type, details) {
    const error = {
      type,
      details,
      timestamp: Date.now(),
      url: window.location.href
    };

    this.errorLog.push(error);
    
    // 只保留最近10个错误
    if (this.errorLog.length > 10) {
      this.errorLog.shift();
    }
  }

  /**
   * 获取用户交互摘要
   */
  getUserInteractionSummary() {
    const summary = {
      totalInteractions: 0,
      recentInteractions: [],
      interactionTypes: {}
    };

    for (const [type, interactions] of this.userInteractionTracker) {
      summary.totalInteractions += interactions.length;
      summary.interactionTypes[type] = interactions.length;
      
      // 添加最近的交互
      const recent = interactions.slice(-3);
      summary.recentInteractions.push(...recent);
    }

    // 按时间排序最近的交互
    summary.recentInteractions.sort((a, b) => b.timestamp - a.timestamp);
    summary.recentInteractions = summary.recentInteractions.slice(0, 10);

    return summary;
  }

  /**
   * 获取最近的错误
   */
  getRecentErrors() {
    return this.errorLog.slice(-5);
  }

  /**
   * 工具方法：检查元素是否可见
   */
  isElementVisible(element) {
    if (!element) return false;
    
    const rect = element.getBoundingClientRect();
    const style = window.getComputedStyle(element);
    
    return (
      rect.width > 0 &&
      rect.height > 0 &&
      style.display !== 'none' &&
      style.visibility !== 'hidden' &&
      style.opacity !== '0'
    );
  }

  /**
   * 工具方法：检查元素是否有重要文本
   */
  hasSignificantText(element) {
    const text = element.textContent?.trim() || '';
    return text.length > 10 && text.length < 1000;
  }

  /**
   * 工具方法：获取元素位置
   */
  getElementPosition(element) {
    const rect = element.getBoundingClientRect();
    return {
      top: rect.top + window.scrollY,
      left: rect.left + window.scrollX,
      width: rect.width,
      height: rect.height
    };
  }

  /**
   * 工具方法：从元素中提取用户名
   */
  extractUsernameFromElement(element) {
    const text = element.textContent?.trim() || '';
    const match = text.match(/用户[：:]\s*([^\s]+)|([^\s]+)(?:\s*已登录)/);
    return match ? (match[1] || match[2]) : null;
  }

  /**
   * 工具方法：获取通知类型
   */
  getNotificationType(element) {
    const className = element.className.toLowerCase();
    
    if (className.includes('error') || className.includes('danger')) return 'error';
    if (className.includes('warning') || className.includes('warn')) return 'warning';
    if (className.includes('success')) return 'success';
    if (className.includes('info')) return 'info';
    
    return 'default';
  }

  /**
   * 触发更新事件
   */
  dispatchUpdateEvent(content) {
    const event = new CustomEvent('pageContentUpdated', {
      detail: content
    });
    window.dispatchEvent(event);
  }

  /**
   * 获取当前页面内容
   */
  getCurrentContent() {
    return this.contentCache.get('current') || this.collectAllContent();
  }

  /**
   * 获取页面内容摘要
   */
  getContentSummary() {
    const content = this.getCurrentContent();
    if (!content) return null;

    return {
      title: content.title,
      url: content.url,
      sectionsCount: content.structure.sections.length,
      headingsCount: content.structure.headings.length,
      buttonsCount: content.interactiveElements.buttons.length,
      linksCount: content.interactiveElements.links.length,
      imagesCount: content.media.totalImages,
      userLoggedIn: content.appState.user?.loggedIn || false,
      hasModals: content.appState.modals.length > 0,
      hasNotifications: content.appState.notifications.length > 0,
      recentInteractions: content.userInteraction.totalInteractions,
      errors: content.errors.length,
      lastUpdate: content.timestamp
    };
  }

  /**
   * 搜索页面内容
   */
  searchContent(query) {
    const content = this.getCurrentContent();
    if (!content) return [];

    const results = [];
    const queryLower = query.toLowerCase();

    // 搜索标题
    content.structure.headings.forEach(heading => {
      if (heading.text.toLowerCase().includes(queryLower)) {
        results.push({
          type: 'heading',
          content: heading.text,
          context: `${heading.level}级标题`,
          relevance: 0.9
        });
      }
    });

    // 搜索可见文本
    content.visibleContent.text.forEach(text => {
      if (text.text.toLowerCase().includes(queryLower)) {
        results.push({
          type: 'text',
          content: text.text.substring(0, 200),
          context: `${text.tag}元素`,
          relevance: 0.7
        });
      }
    });

    // 搜索按钮
    content.interactiveElements.buttons.forEach(button => {
      if (button.text.toLowerCase().includes(queryLower)) {
        results.push({
          type: 'button',
          content: button.text,
          context: '可点击按钮',
          relevance: 0.8
        });
      }
    });

    // 搜索链接
    content.interactiveElements.links.forEach(link => {
      if (link.text.toLowerCase().includes(queryLower)) {
        results.push({
          type: 'link',
          content: link.text,
          context: `链接到: ${link.href}`,
          relevance: 0.8
        });
      }
    });

    return results.sort((a, b) => b.relevance - a.relevance);
  }

  /**
   * 检测React应用状态（新增）
   */
  detectReactState() {
    try {
      // 检测React DevTools
      const hasReactDevTools = window.__REACT_DEVTOOLS_GLOBAL_HOOK__;

      // 检测React根元素
      const reactRoots = document.querySelectorAll('[data-reactroot], #root, #app');

      // 检测React组件
      const reactComponents = this.findReactComponents();

      return {
        hasReact: hasReactDevTools || reactRoots.length > 0,
        rootElements: Array.from(reactRoots).map(el => ({
          id: el.id,
          className: el.className
        })),
        componentCount: reactComponents.length,
        components: reactComponents.slice(0, 10), // 限制数量
        stateManagement: this.detectStateManagement()
      };
    } catch (error) {
      console.error('React状态检测失败:', error);
      return { hasReact: false, error: error.message };
    }
  }

  /**
   * 查找React组件（新增）
   */
  findReactComponents() {
    const components = [];
    const elements = document.querySelectorAll('*');

    elements.forEach(el => {
      // 检查是否有React fiber
      const fiberKey = Object.keys(el).find(key => key.startsWith('__reactFiber'));
      if (fiberKey) {
        const fiber = el[fiberKey];
        if (fiber && fiber.type && typeof fiber.type === 'function') {
          components.push({
            name: fiber.type.name || 'Anonymous',
            props: Object.keys(fiber.memoizedProps || {}),
            hasState: !!fiber.memoizedState
          });
        }
      }
    });

    return components;
  }

  /**
   * 检测状态管理（新增）
   */
  detectStateManagement() {
    const stateManagers = [];

    // 检测Redux
    if (window.__REDUX_DEVTOOLS_EXTENSION__) {
      stateManagers.push('Redux DevTools');
    }

    // 检测Zustand
    if (window.__zustand__) {
      stateManagers.push('Zustand');
    }

    // 检测Context API使用
    const contextProviders = document.querySelectorAll('[data-context-provider]');
    if (contextProviders.length > 0) {
      stateManagers.push(`Context API (${contextProviders.length} providers)`);
    }

    return stateManagers;
  }

  /**
   * 识别功能区域（新增）
   */
  identifyFunctionalAreas() {
    const areas = [];

    // 导航区域
    const navElements = document.querySelectorAll('nav, .navbar, .navigation, [role="navigation"]');
    if (navElements.length > 0) {
      areas.push({
        type: 'navigation',
        count: navElements.length,
        items: Array.from(navElements).map(nav => ({
          text: nav.textContent?.substring(0, 100) || '',
          links: nav.querySelectorAll('a').length
        }))
      });
    }

    // 表单区域
    const forms = document.querySelectorAll('form');
    if (forms.length > 0) {
      areas.push({
        type: 'forms',
        count: forms.length,
        items: Array.from(forms).map(form => ({
          action: form.action || '',
          method: form.method || 'GET',
          fields: form.querySelectorAll('input, select, textarea').length
        }))
      });
    }

    // 数据展示区域
    const tables = document.querySelectorAll('table, .table, .data-grid');
    if (tables.length > 0) {
      areas.push({
        type: 'data-display',
        count: tables.length,
        items: Array.from(tables).map(table => ({
          rows: table.querySelectorAll('tr').length,
          columns: table.querySelectorAll('th, td').length
        }))
      });
    }

    // 内容区域
    const contentAreas = document.querySelectorAll('main, .content, .main-content, article');
    if (contentAreas.length > 0) {
      areas.push({
        type: 'content',
        count: contentAreas.length,
        items: Array.from(contentAreas).map(area => ({
          headings: area.querySelectorAll('h1, h2, h3, h4, h5, h6').length,
          paragraphs: area.querySelectorAll('p').length,
          wordCount: (area.textContent || '').split(/\s+/).length
        }))
      });
    }

    return areas;
  }

  /**
   * 分析工作流状态（新增）
   */
  analyzeWorkflowState() {
    const workflow = {
      currentStep: null,
      availableActions: [],
      completedSteps: [],
      nextSteps: [],
      blockers: []
    };

    // 检测步骤指示器
    const stepIndicators = document.querySelectorAll('.step, .wizard-step, .progress-step, [data-step]');
    if (stepIndicators.length > 0) {
      stepIndicators.forEach((step, index) => {
        const isActive = step.classList.contains('active') || step.classList.contains('current');
        const isCompleted = step.classList.contains('completed') || step.classList.contains('done');

        const stepInfo = {
          index: index + 1,
          text: step.textContent?.trim() || `步骤 ${index + 1}`,
          status: isActive ? 'current' : isCompleted ? 'completed' : 'pending'
        };

        if (isActive) workflow.currentStep = stepInfo;
        if (isCompleted) workflow.completedSteps.push(stepInfo);
      });
    }

    // 检测可用操作
    const actionButtons = document.querySelectorAll('button:not([disabled]), .btn:not(.disabled), [role="button"]:not([disabled])');
    workflow.availableActions = Array.from(actionButtons).map(btn => ({
      text: btn.textContent?.trim() || '',
      type: this.classifyButtonType(btn),
      primary: btn.classList.contains('primary') || btn.classList.contains('btn-primary')
    })).slice(0, 10);

    // 检测阻塞因素
    const disabledElements = document.querySelectorAll('[disabled], .disabled');
    const errorMessages = document.querySelectorAll('.error, .alert-danger, .validation-error');

    if (disabledElements.length > 0) {
      workflow.blockers.push(`${disabledElements.length} 个元素被禁用`);
    }

    if (errorMessages.length > 0) {
      workflow.blockers.push(`${errorMessages.length} 个错误消息`);
    }

    return workflow;
  }

  /**
   * 分析页面可用性（新增）
   */
  analyzeAccessibility() {
    const accessibility = {
      score: 0,
      issues: [],
      recommendations: []
    };

    let score = 100;

    // 检查图片alt属性
    const imagesWithoutAlt = document.querySelectorAll('img:not([alt])');
    if (imagesWithoutAlt.length > 0) {
      accessibility.issues.push(`${imagesWithoutAlt.length} 个图片缺少alt属性`);
      accessibility.recommendations.push('为图片添加描述性的alt属性');
      score -= 10;
    }

    // 检查表单标签
    const inputsWithoutLabels = document.querySelectorAll('input:not([aria-label]):not([aria-labelledby])');
    const labelsCount = document.querySelectorAll('label').length;
    if (inputsWithoutLabels.length > labelsCount) {
      accessibility.issues.push('部分表单字段缺少标签');
      accessibility.recommendations.push('为表单字段添加label或aria-label');
      score -= 15;
    }

    // 检查标题层级
    const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
    if (headings.length === 0) {
      accessibility.issues.push('页面缺少标题结构');
      accessibility.recommendations.push('添加适当的标题层级结构');
      score -= 20;
    }

    // 检查焦点管理
    const focusableElements = document.querySelectorAll('a, button, input, select, textarea, [tabindex]');
    const elementsWithTabIndex = document.querySelectorAll('[tabindex]');
    if (focusableElements.length > 0 && elementsWithTabIndex.length === 0) {
      accessibility.recommendations.push('考虑优化键盘导航体验');
    }

    accessibility.score = Math.max(0, score);

    return accessibility;
  }

  /**
   * 分类按钮类型（新增）
   */
  classifyButtonType(button) {
    const text = button.textContent?.toLowerCase() || '';
    const className = button.className.toLowerCase();

    if (text.includes('保存') || text.includes('提交') || text.includes('确认')) return 'submit';
    if (text.includes('取消') || text.includes('关闭')) return 'cancel';
    if (text.includes('删除') || text.includes('移除')) return 'delete';
    if (text.includes('编辑') || text.includes('修改')) return 'edit';
    if (text.includes('添加') || text.includes('新建') || text.includes('创建')) return 'create';
    if (text.includes('搜索') || text.includes('查找')) return 'search';
    if (className.includes('primary')) return 'primary';
    if (className.includes('secondary')) return 'secondary';

    return 'action';
  }

  /**
   * 提取用户头像（新增）
   */
  extractUserAvatar(userElement) {
    const avatar = userElement.querySelector('img, .avatar, .user-avatar');
    return avatar ? avatar.src || avatar.style.backgroundImage : null;
  }

  /**
   * 检测用户角色（新增）
   */
  detectUserRole() {
    const roleIndicators = document.querySelectorAll('[data-role], .role, .user-role');
    if (roleIndicators.length > 0) {
      return roleIndicators[0].textContent?.trim() || roleIndicators[0].dataset.role;
    }

    // 从URL或页面内容推断角色
    const path = window.location.pathname.toLowerCase();
    if (path.includes('admin')) return 'admin';
    if (path.includes('manager')) return 'manager';
    if (path.includes('user')) return 'user';

    return 'unknown';
  }

  /**
   * 停止收集
   */
  stopCollection() {
    this.isCollecting = false;
    
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
    
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
      this.debounceTimer = null;
    }
    
    this.observers.forEach(observer => {
      observer.disconnect();
    });
    this.observers.clear();
  }

  /**
   * 重置收集器
   */
  reset() {
    this.stopCollection();
    this.contentCache.clear();
    this.userInteractionTracker.clear();
    this.errorLog.length = 0;
    this.lastUpdate = null;
  }

  /**
   * 获取统计信息
   */
  getStats() {
    return {
      isCollecting: this.isCollecting,
      isInitialized: this.isInitialized,
      lastUpdate: this.lastUpdate,
      cacheSize: this.contentCache.size,
      observersCount: this.observers.size,
      interactionTypes: Array.from(this.userInteractionTracker.keys()),
      errorCount: this.errorLog.length,
      totalInteractions: Array.from(this.userInteractionTracker.values())
        .reduce((sum, arr) => sum + arr.length, 0)
    };
  }

  /**
   * 分析页面下载内容（新增）
   */
  analyzeDownloadContent() {
    const startTime = performance.now();
    console.log('[Download Analysis] 开始分析下载内容');

    const downloadContent = {
      tools: [],
      software: [],
      documents: [],
      resources: [],
      downloadButtons: [],
      downloadLinks: [],
      categories: [],
      totalDownloads: 0,
      hasDownloadSection: false
    };

    try {
      // 识别下载相关的容器
      const containerStart = performance.now();
      const downloadContainers = this.findDownloadContainers();
      downloadContent.hasDownloadSection = downloadContainers.length > 0;
      console.log(`[Download Analysis] 容器识别完成，耗时: ${(performance.now() - containerStart).toFixed(2)}ms，找到 ${downloadContainers.length} 个容器`);

      // 分析工具下载
      const toolStart = performance.now();
      downloadContent.tools = this.extractToolDownloads();
      console.log(`[Download Analysis] 工具提取完成，耗时: ${(performance.now() - toolStart).toFixed(2)}ms，找到 ${downloadContent.tools.length} 个工具`);

      // 分析软件下载
      const softwareStart = performance.now();
      downloadContent.software = this.extractSoftwareDownloads();
      console.log(`[Download Analysis] 软件提取完成，耗时: ${(performance.now() - softwareStart).toFixed(2)}ms，找到 ${downloadContent.software.length} 个软件`);

      // 分析文档下载
      const docStart = performance.now();
      downloadContent.documents = this.extractDocumentDownloads();
      console.log(`[Download Analysis] 文档提取完成，耗时: ${(performance.now() - docStart).toFixed(2)}ms，找到 ${downloadContent.documents.length} 个文档`);

      // 分析其他资源下载
      const resourceStart = performance.now();
      downloadContent.resources = this.extractResourceDownloads();
      console.log(`[Download Analysis] 资源提取完成，耗时: ${(performance.now() - resourceStart).toFixed(2)}ms，找到 ${downloadContent.resources.length} 个资源`);

      // 收集所有下载按钮
      const buttonStart = performance.now();
      downloadContent.downloadButtons = this.collectDownloadButtons();
      console.log(`[Download Analysis] 按钮收集完成，耗时: ${(performance.now() - buttonStart).toFixed(2)}ms，找到 ${downloadContent.downloadButtons.length} 个按钮`);

      // 收集所有下载链接
      const linkStart = performance.now();
      downloadContent.downloadLinks = this.collectDownloadLinks();
      console.log(`[Download Analysis] 链接收集完成，耗时: ${(performance.now() - linkStart).toFixed(2)}ms，找到 ${downloadContent.downloadLinks.length} 个链接`);

      // 识别下载分类
      const categoryStart = performance.now();
      downloadContent.categories = this.identifyDownloadCategories();
      console.log(`[Download Analysis] 分类识别完成，耗时: ${(performance.now() - categoryStart).toFixed(2)}ms，找到 ${downloadContent.categories.length} 个分类`);

      // 计算总下载数量
      downloadContent.totalDownloads =
        downloadContent.tools.length +
        downloadContent.software.length +
        downloadContent.documents.length +
        downloadContent.resources.length;

    } catch (error) {
      console.warn('[Download Analysis] 下载内容分析失败:', error);
    }

    const totalTime = performance.now() - startTime;
    console.log(`[Download Analysis] 分析完成，总耗时: ${totalTime.toFixed(2)}ms`);
    console.log('[Download Analysis] 分析结果:', downloadContent);

    return downloadContent;
  }

  /**
   * 查找下载相关的容器元素
   */
  findDownloadContainers() {
    // 检查DOM是否可用
    if (!document || !document.body) {
      console.warn('[Download Containers] DOM未准备就绪');
      return [];
    }

    const selectors = [
      // 通用下载相关选择器
      '[class*="download"]',
      '[id*="download"]',
      '[class*="tool"]',
      '[id*="tool"]',
      '[class*="software"]',
      '[id*="software"]',
      '.downloads',
      '.tools',
      '.software-list',
      '.resource-list',
      '[data-download]',
      '[data-tool]',

      // 针对当前页面结构的特定选择器
      'section[id*="downloads"]',
      'div[id*="downloads"]',
      'section:has(h2:contains("工具下载"))',
      'section:has(h3:contains("工具下载"))',
      'div:has(h2:contains("工具下载"))',
      'div:has(h3:contains("工具下载"))',

      // 查找包含工具下载相关文本的容器
      '*:has(h2:contains("AI 开发工具"))',
      '*:has(h3:contains("AI 开发工具"))',
      '*:has(h2:contains("开发工具"))',
      '*:has(h3:contains("开发工具"))'
    ];

    const containers = [];
    const containerSet = new Set(); // 使用Set避免重复检查

    // 首先尝试通过文本内容查找
    try {
      const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
      headings.forEach(heading => {
        const text = heading.textContent?.toLowerCase() || '';
        if (text.includes('工具下载') || text.includes('开发工具') || text.includes('ai 开发工具')) {
          const container = heading.closest('section') || heading.closest('div[class*="section"]') || heading.parentElement;
          if (container && !containerSet.has(container)) {
            containerSet.add(container);
            containers.push(container);
            console.log('[Download Containers] 通过标题找到容器:', heading.textContent);
          }
        }
      });
    } catch (e) {
      console.warn('[Download Containers] 标题搜索失败:', e.message);
    }

    // 然后使用选择器查找
    selectors.forEach(selector => {
      try {
        const elements = document.querySelectorAll(selector);
        elements.forEach(el => {
          // 确保元素仍然在DOM中
          if (el && el.parentNode && !containerSet.has(el)) {
            containerSet.add(el);
            containers.push(el);
          }
        });
      } catch (e) {
        console.warn(`[Download Containers] 选择器失败: ${selector}`, e.message);
      }
    });

    console.log(`[Download Containers] 找到 ${containers.length} 个下载容器`);
    return containers;
  }

  /**
   * 提取工具下载信息
   */
  extractToolDownloads() {
    const tools = [];
    const toolKeywords = ['工具', 'tool', 'plugin', '插件', 'extension', '扩展', 'vs code', 'cline', 'roocode', 'jetbrains', 'ide', 'assistant'];

    console.log('[Tool Extraction] 开始提取工具下载信息');

    // 首先尝试直接查找已知的工具
    try {
      const knownTools = [
        { name: 'VS Code IDE', selector: '*:contains("VS Code")' },
        { name: 'Cline插件', selector: '*:contains("Cline")' },
        { name: 'RooCode插件', selector: '*:contains("RooCode")' },
        { name: 'JetBrains AI Assistant', selector: '*:contains("JetBrains")' }
      ];

      knownTools.forEach(({ name, selector }) => {
        try {
          const elements = document.querySelectorAll('h4, h3, h2, div, span');
          elements.forEach(el => {
            const text = el.textContent || '';
            if (text.includes(name.split(' ')[0]) || text.includes(name)) {
              const toolCard = el.closest('div[class*="card"]') ||
                             el.closest('div[class*="item"]') ||
                             el.closest('div[class*="tool"]') ||
                             el.parentElement;

              if (toolCard) {
                const tool = this.extractDownloadInfo(toolCard, 'tool');
                if (tool && tool.name) {
                  tools.push(tool);
                  console.log(`[Tool Extraction] 找到工具: ${tool.name}`);
                }
              }
            }
          });
        } catch (e) {
          console.warn(`[Tool Extraction] 查找 ${name} 失败:`, e.message);
        }
      });
    } catch (e) {
      console.warn('[Tool Extraction] 直接查找工具失败:', e.message);
    }

    // 如果直接查找没有结果，使用关键词搜索
    if (tools.length === 0) {
      console.log('[Tool Extraction] 直接查找无结果，使用关键词搜索');
      const toolElements = this.findElementsByKeywords(toolKeywords);

      toolElements.forEach(element => {
        const tool = this.extractDownloadInfo(element, 'tool');
        if (tool && tool.name) {
          // 避免重复添加
          const exists = tools.some(t => t.name === tool.name);
          if (!exists) {
            tools.push(tool);
            console.log(`[Tool Extraction] 通过关键词找到工具: ${tool.name}`);
          }
        }
      });
    }

    console.log(`[Tool Extraction] 提取完成，找到 ${tools.length} 个工具`);
    return tools;
  }

  /**
   * 提取软件下载信息
   */
  extractSoftwareDownloads() {
    const software = [];
    const softwareKeywords = ['软件', 'software', 'app', '应用', 'program', '程序'];

    const softwareElements = this.findElementsByKeywords(softwareKeywords);

    softwareElements.forEach(element => {
      const app = this.extractDownloadInfo(element, 'software');
      if (app) {
        software.push(app);
      }
    });

    return software;
  }

  /**
   * 提取文档下载信息
   */
  extractDocumentDownloads() {
    const documents = [];
    const docKeywords = ['文档', 'document', 'doc', 'pdf', '手册', 'manual', '指南', 'guide'];

    const docElements = this.findElementsByKeywords(docKeywords);

    docElements.forEach(element => {
      const doc = this.extractDownloadInfo(element, 'document');
      if (doc) {
        documents.push(doc);
      }
    });

    return documents;
  }

  /**
   * 提取其他资源下载信息
   */
  extractResourceDownloads() {
    const resources = [];
    const resourceKeywords = ['资源', 'resource', '素材', 'asset', '模板', 'template'];

    const resourceElements = this.findElementsByKeywords(resourceKeywords);

    resourceElements.forEach(element => {
      const resource = this.extractDownloadInfo(element, 'resource');
      if (resource) {
        resources.push(resource);
      }
    });

    return resources;
  }

  /**
   * 根据关键词查找元素（优化版）
   */
  findElementsByKeywords(keywords) {
    const startTime = performance.now();
    console.log(`[Keyword Search] 开始搜索关键词:`, keywords);

    // 检查DOM是否可用
    if (!document || !document.body) {
      console.warn('[Keyword Search] DOM未准备就绪');
      return [];
    }

    const elements = [];
    const elementSet = new Set(); // 使用Set避免重复

    // 优化：使用更高效的选择器而不是XPath
    const commonSelectors = [
      'h1, h2, h3, h4, h5, h6',
      'button, a[href]',
      '[class*="download"], [class*="tool"], [class*="software"]',
      '[id*="download"], [id*="tool"], [id*="software"]',
      'li, div, span, p'
    ];

    // 针对当前页面的特定选择器
    const specificSelectors = [
      'div:has(h4:contains("VS Code"))',
      'div:has(h4:contains("Cline"))',
      'div:has(h4:contains("RooCode"))',
      'div:has(h4:contains("JetBrains"))',
      'div:has(h4:contains("插件"))',
      'div:has(h4:contains("IDE"))',
      'div:has(h4:contains("Assistant"))'
    ];

    keywords.forEach(keyword => {
      const keywordLower = keyword.toLowerCase();

      // 首先尝试更精确的选择器
      [...commonSelectors, ...specificSelectors].forEach(selector => {
        try {
          const candidateElements = document.querySelectorAll(selector);
          candidateElements.forEach(el => {
            // 确保元素仍然在DOM中且有内容
            if (el && el.parentNode && el.textContent && el.textContent.toLowerCase().includes(keywordLower)) {
              if (!elementSet.has(el)) {
                elementSet.add(el);
                elements.push(el);
                console.log(`[Keyword Search] 找到匹配元素: ${el.tagName} - ${el.textContent?.substring(0, 50)}...`);
              }
            }
          });
        } catch (e) {
          console.warn(`[Keyword Search] 选择器失败: ${selector}`, e.message);
        }
      });
    });

    // 特殊处理：直接查找工具卡片
    try {
      const toolCards = document.querySelectorAll('div[class*="card"], div[class*="item"], div[class*="tool"]');
      toolCards.forEach(card => {
        const text = card.textContent?.toLowerCase() || '';
        const hasToolKeyword = keywords.some(keyword => text.includes(keyword.toLowerCase()));
        const hasToolContent = text.includes('vs code') || text.includes('cline') || text.includes('roocode') ||
                              text.includes('jetbrains') || text.includes('插件') || text.includes('版本');

        if ((hasToolKeyword || hasToolContent) && !elementSet.has(card)) {
          elementSet.add(card);
          elements.push(card);
          console.log(`[Keyword Search] 找到工具卡片: ${card.textContent?.substring(0, 100)}...`);
        }
      });
    } catch (e) {
      console.warn('[Keyword Search] 工具卡片搜索失败:', e.message);
    }

    // 如果没有找到足够的元素，使用备用方法（限制范围）
    if (elements.length < 3) {
      console.log(`[Keyword Search] 精确搜索结果不足，使用备用方法`);
      keywords.forEach(keyword => {
        const keywordLower = keyword.toLowerCase();

        try {
          // 限制搜索范围，避免性能问题
          const searchContainers = document.querySelectorAll('main, .content, .container, body > div');
          const containersToSearch = searchContainers.length > 0 ? searchContainers : [document.body];

          containersToSearch.forEach(container => {
            if (!container || !container.parentNode) return; // 确保容器仍在DOM中

            try {
              const textElements = container.querySelectorAll('*');
              for (let i = 0; i < Math.min(textElements.length, 200); i++) { // 增加搜索数量
                const el = textElements[i];
                if (el && el.parentNode && el.textContent && el.textContent.toLowerCase().includes(keywordLower)) {
                  if (!elementSet.has(el)) {
                    elementSet.add(el);
                    elements.push(el);
                  }
                }
              }
            } catch (containerError) {
              console.warn(`[Keyword Search] 容器搜索失败:`, containerError.message);
            }
          });
        } catch (backupError) {
          console.warn(`[Keyword Search] 备用搜索失败:`, backupError.message);
        }
      });
    }

    const searchTime = performance.now() - startTime;
    console.log(`[Keyword Search] 搜索完成，耗时: ${searchTime.toFixed(2)}ms，找到 ${elements.length} 个元素`);

    return elements;
  }

  /**
   * 从元素中提取下载信息
   */
  extractDownloadInfo(element, type) {
    try {
      const info = {
        type: type,
        name: '',
        description: '',
        version: '',
        size: '',
        platform: '',
        downloadUrl: '',
        downloadText: '',
        element: element.tagName,
        position: this.getElementPosition(element)
      };

      // 提取名称
      info.name = this.extractName(element);

      // 提取描述
      info.description = this.extractDescription(element);

      // 提取版本信息
      info.version = this.extractVersion(element);

      // 提取文件大小
      info.size = this.extractFileSize(element);

      // 提取平台信息
      info.platform = this.extractPlatform(element);

      // 提取下载链接
      const downloadLink = this.findDownloadLink(element);
      if (downloadLink) {
        info.downloadUrl = downloadLink.href || downloadLink.getAttribute('data-url') || '';
        info.downloadText = downloadLink.textContent?.trim() || '';
      }

      // 只返回有效的下载信息
      if (info.name || info.downloadUrl || info.downloadText) {
        return info;
      }
    } catch (error) {
      console.warn('提取下载信息失败:', error);
    }

    return null;
  }

  /**
   * 提取名称
   */
  extractName(element) {
    console.log('[Extract Name] 开始提取名称，元素:', element.tagName, element.className);

    // 查找标题元素
    const titleSelectors = ['h1', 'h2', 'h3', 'h4', 'h5', 'h6', '.title', '.name', '[data-name]'];

    for (const selector of titleSelectors) {
      try {
        const titleEl = element.querySelector(selector) ||
                       (element.matches(selector) ? element : null);
        if (titleEl && titleEl.textContent?.trim()) {
          const name = titleEl.textContent.trim();
          console.log(`[Extract Name] 通过选择器 ${selector} 找到名称: ${name}`);
          return name;
        }
      } catch (e) {
        console.warn(`[Extract Name] 选择器 ${selector} 失败:`, e.message);
      }
    }

    // 特殊处理：查找包含工具名称的文本
    try {
      const text = element.textContent || '';
      const lines = text.split('\n').map(line => line.trim()).filter(line => line.length > 0);

      for (const line of lines) {
        // 查找包含已知工具名称的行
        if (line.includes('VS Code') || line.includes('Cline') || line.includes('RooCode') ||
            line.includes('JetBrains') || line.includes('插件') || line.includes('IDE')) {
          if (line.length < 100) { // 避免选择过长的文本
            console.log(`[Extract Name] 通过文本匹配找到名称: ${line}`);
            return line;
          }
        }
      }

      // 如果没有找到特定工具名称，使用第一行非空文本
      if (lines.length > 0 && lines[0].length < 100) {
        console.log(`[Extract Name] 使用第一行文本作为名称: ${lines[0]}`);
        return lines[0];
      }
    } catch (e) {
      console.warn('[Extract Name] 文本处理失败:', e.message);
    }

    console.log('[Extract Name] 未找到有效名称');
    return '';
  }

  /**
   * 提取描述
   */
  extractDescription(element) {
    console.log('[Extract Description] 开始提取描述');

    const descSelectors = ['.description', '.desc', '.summary', '.info', 'p'];

    for (const selector of descSelectors) {
      try {
        const descEl = element.querySelector(selector);
        if (descEl && descEl.textContent?.trim()) {
          const desc = descEl.textContent.trim();
          if (desc.length > 10 && desc.length < 500) {
            console.log(`[Extract Description] 通过选择器 ${selector} 找到描述: ${desc.substring(0, 50)}...`);
            return desc;
          }
        }
      } catch (e) {
        console.warn(`[Extract Description] 选择器 ${selector} 失败:`, e.message);
      }
    }

    // 尝试从文本内容中提取描述
    try {
      const text = element.textContent || '';
      const lines = text.split('\n').map(line => line.trim()).filter(line => line.length > 0);

      // 查找描述性文本（通常在标题后面）
      for (let i = 1; i < lines.length; i++) {
        const line = lines[i];
        if (line.length > 10 && line.length < 500 &&
            !line.includes('版本') && !line.includes('需要登录') &&
            !line.includes('推荐') && !line.match(/^v?\d+\./)) {
          console.log(`[Extract Description] 从文本中找到描述: ${line}`);
          return line;
        }
      }
    } catch (e) {
      console.warn('[Extract Description] 文本提取失败:', e.message);
    }

    console.log('[Extract Description] 未找到有效描述');
    return '';
  }

  /**
   * 提取版本信息
   */
  extractVersion(element) {
    const text = element.textContent || '';
    const versionPatterns = [
      /v?(\d+\.\d+\.\d+)/i,
      /version\s*:?\s*(\d+\.\d+\.\d+)/i,
      /版本\s*:?\s*(\d+\.\d+\.\d+)/i
    ];

    for (const pattern of versionPatterns) {
      const match = text.match(pattern);
      if (match) {
        return match[1];
      }
    }

    return '';
  }

  /**
   * 提取文件大小
   */
  extractFileSize(element) {
    const text = element.textContent || '';
    const sizePatterns = [
      /(\d+(?:\.\d+)?\s*(?:KB|MB|GB|TB))/i,
      /大小\s*:?\s*(\d+(?:\.\d+)?\s*(?:KB|MB|GB|TB))/i,
      /size\s*:?\s*(\d+(?:\.\d+)?\s*(?:KB|MB|GB|TB))/i
    ];

    for (const pattern of sizePatterns) {
      const match = text.match(pattern);
      if (match) {
        return match[1];
      }
    }

    return '';
  }

  /**
   * 提取平台信息
   */
  extractPlatform(element) {
    const text = element.textContent?.toLowerCase() || '';
    const platforms = [];

    const platformKeywords = {
      'windows': ['windows', 'win', 'pc'],
      'macos': ['macos', 'mac', 'osx'],
      'linux': ['linux', 'ubuntu', 'debian'],
      'android': ['android'],
      'ios': ['ios', 'iphone', 'ipad'],
      'web': ['web', 'browser', '浏览器']
    };

    Object.entries(platformKeywords).forEach(([platform, keywords]) => {
      if (keywords.some(keyword => text.includes(keyword))) {
        platforms.push(platform);
      }
    });

    return platforms.join(', ');
  }

  /**
   * 查找下载链接
   */
  findDownloadLink(element) {
    // 查找直接的下载链接
    const downloadLink = element.querySelector('a[href*="download"], a[href*=".exe"], a[href*=".dmg"], a[href*=".deb"], a[href*=".zip"]');
    if (downloadLink) {
      return downloadLink;
    }

    // 查找下载按钮
    const downloadButton = element.querySelector('button[data-download], .download-btn, .btn-download');
    if (downloadButton) {
      return downloadButton;
    }

    // 如果元素本身是链接
    if (element.tagName === 'A' && element.href) {
      return element;
    }

    return null;
  }

  /**
   * 收集所有下载按钮
   */
  collectDownloadButtons() {
    const buttons = [];
    const buttonSelectors = [
      'button[class*="download"]',
      'button[id*="download"]',
      '.download-btn',
      '.btn-download',
      'button[data-download]',
      'input[type="button"][value*="下载"]',
      'input[type="submit"][value*="下载"]'
    ];

    buttonSelectors.forEach(selector => {
      try {
        const elements = document.querySelectorAll(selector);
        elements.forEach(button => {
          buttons.push({
            text: button.textContent?.trim() || button.value || '',
            type: button.type || 'button',
            disabled: button.disabled,
            dataUrl: button.getAttribute('data-url') || button.getAttribute('data-download'),
            position: this.getElementPosition(button)
          });
        });
      } catch (e) {
        // 忽略无效选择器
      }
    });

    return buttons;
  }

  /**
   * 收集所有下载链接
   */
  collectDownloadLinks() {
    const links = [];
    const linkSelectors = [
      'a[href*="download"]',
      'a[href*=".exe"]',
      'a[href*=".dmg"]',
      'a[href*=".deb"]',
      'a[href*=".zip"]',
      'a[href*=".rar"]',
      'a[href*=".tar"]',
      'a[href*=".pdf"]',
      'a[class*="download"]',
      'a[id*="download"]'
    ];

    linkSelectors.forEach(selector => {
      try {
        const elements = document.querySelectorAll(selector);
        elements.forEach(link => {
          links.push({
            text: link.textContent?.trim() || '',
            href: link.href,
            target: link.target,
            download: link.download,
            fileType: this.getFileTypeFromUrl(link.href),
            position: this.getElementPosition(link)
          });
        });
      } catch (e) {
        // 忽略无效选择器
      }
    });

    return links;
  }

  /**
   * 识别下载分类
   */
  identifyDownloadCategories() {
    const categories = [];
    const categorySelectors = [
      '.category',
      '.section',
      '.group',
      'h2',
      'h3',
      '.tab',
      '[data-category]'
    ];

    categorySelectors.forEach(selector => {
      try {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
          const text = element.textContent?.trim() || '';
          if (text && this.isDownloadCategory(text)) {
            categories.push({
              name: text,
              element: element.tagName,
              position: this.getElementPosition(element)
            });
          }
        });
      } catch (e) {
        // 忽略无效选择器
      }
    });

    return categories;
  }

  /**
   * 判断是否为下载分类
   */
  isDownloadCategory(text) {
    const categoryKeywords = [
      '工具', 'tool', '软件', 'software', '插件', 'plugin',
      '扩展', 'extension', '应用', 'app', '程序', 'program',
      '文档', 'document', '资源', 'resource', '下载', 'download'
    ];

    return categoryKeywords.some(keyword =>
      text.toLowerCase().includes(keyword.toLowerCase())
    );
  }

  /**
   * 从URL获取文件类型
   */
  getFileTypeFromUrl(url) {
    if (!url) return '';

    const extension = url.split('.').pop()?.toLowerCase();
    const fileTypes = {
      'exe': 'Windows可执行文件',
      'dmg': 'macOS安装包',
      'deb': 'Debian安装包',
      'rpm': 'RPM安装包',
      'zip': '压缩文件',
      'rar': '压缩文件',
      'tar': '压缩文件',
      'pdf': 'PDF文档',
      'doc': 'Word文档',
      'docx': 'Word文档'
    };

    return fileTypes[extension] || extension || '';
  }

  /**
   * 获取元素位置信息
   */
  getElementPosition(element) {
    try {
      const rect = element.getBoundingClientRect();
      return {
        top: Math.round(rect.top),
        left: Math.round(rect.left),
        width: Math.round(rect.width),
        height: Math.round(rect.height),
        visible: rect.width > 0 && rect.height > 0
      };
    } catch (e) {
      return { top: 0, left: 0, width: 0, height: 0, visible: false };
    }
  }
}

// 创建全局实例
const pageContentCollector = new PageContentCollector();

export default pageContentCollector;
export { PageContentCollector }; 