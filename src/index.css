@tailwind base;
@tailwind components;
@tailwind utilities;

/* 基础样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* 平滑滚动 */
html {
  scroll-behavior: smooth;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 10px;
}

::-webkit-scrollbar-track {
  background: #1a1a1a;
}

::-webkit-scrollbar-thumb {
  background: #333;
  border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* 选择文本样式 */
::selection {
  background-color: rgba(6, 182, 212, 0.3);
  color: #fff;
}

/* 防止移动端双击缩放 */
body {
  touch-action: manipulation;
}

/* 确保最小高度 */
#root {
  min-height: 100vh;
}

/* 动画性能优化 */
.will-change-transform {
  will-change: transform;
}

/* 自定义动画 */
@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes pulse-slow {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes gradient {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* 轻量化动画 - 替代framer-motion */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
    transform: translate3d(0,0,0);
  }
  40%, 43% {
    animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
    transform: translate3d(0, -30px, 0);
  }
  70% {
    animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0,-4px,0);
  }
}

/* 新增动画类 */
.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out forwards;
}

.animate-scale-in {
  animation: scaleIn 0.6s ease-out forwards;
}

.animate-pulse-slow {
  animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* 性能优化的动画属性 */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

/* GPU 加速的变换 */
.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
}

.animate-gradient {
  animation: gradient 6s ease infinite;
}

/* 防止图标闪烁 */
.material-icons,
.fa {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 内网环境emoji字体支持 */
body, 
html {
  font-family: 
    -apple-system, 
    BlinkMacSystemFont, 
    "Segoe UI", 
    "Roboto", 
    "Helvetica Neue", 
    Arial, 
    "Noto Sans",
    "Liberation Sans", 
    sans-serif, 
    "Apple Color Emoji", 
    "Segoe UI Emoji", 
    "Segoe UI Symbol", 
    "Noto Color Emoji";
}

/* 内网环境下emoji回退方案 */
.emoji-fallback {
  font-family: 
    "Apple Color Emoji", 
    "Segoe UI Emoji", 
    "Noto Color Emoji", 
    "Segoe UI Symbol", 
    "Symbola",
    serif;
}

/* 确保关键emoji在所有环境下都能显示 */
.emoji-rocket::before {
  content: "🚀";
  font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", "Segoe UI Symbol", serif;
}

/* 如果emoji不支持，则使用FontAwesome图标替代 */
@supports not (font-family: "Apple Color Emoji") {
  .emoji-rocket::before {
    content: "";
    font-family: "Font Awesome 6 Free";
    font-weight: 900;
  }
}

/* 如果emoji不支持，提供回退方案 */
.emoji-rocket::before {
  content: "🚀";
}

/* 内网环境下可能需要的emoji回退 */
@media (max-width: 600px) {
  .emoji-rocket::before {
    content: "[Rocket]";
  }
}

/* ===========================================
   自定义AssistantMessage样式优化
   ========================================== */

/* 修复CopilotKit消息文字颜色问题 - 全面覆盖，但排除代码块 */
.copilotKitMarkdownElement:not(.copilotKitCodeBlock):not(.copilotKitCodeBlock *),
.copilotKitMarkdownElement p:not(.copilotKitCodeBlock *),
.copilotKitMarkdownElement span:not(.copilotKitCodeBlock *),
.copilotKitMarkdownElement div:not(.copilotKitCodeBlock *),
.copilotKitMarkdownElement strong:not(.copilotKitCodeBlock *),
.copilotKitMarkdownElement em:not(.copilotKitCodeBlock *),
.copilotKitMarkdownElement b:not(.copilotKitCodeBlock *),
.copilotKitMarkdownElement i:not([class*="fa"]):not(.copilotKitCodeBlock *),
.copilotKitMarkdownElement li:not(.copilotKitCodeBlock *),
.copilotKitMarkdownElement ul:not(.copilotKitCodeBlock *),
.copilotKitMarkdownElement ol:not(.copilotKitCodeBlock *) {
  color: #1f2937 !important; /* 深灰色文字，确保可见 */
  line-height: 1.6;
}

/* 确保整个助手消息区域有正确的文字颜色，但排除代码块 */
.copilotKitAssistantMessage .copilotKitMarkdown:not(.copilotKitCodeBlock),
.copilotKitAssistantMessage .copilotKitMarkdown p:not(.copilotKitCodeBlock *),
.copilotKitAssistantMessage .copilotKitMarkdown span:not(.copilotKitCodeBlock *),
.copilotKitAssistantMessage .copilotKitMarkdown div:not(.copilotKitCodeBlock *),
.copilotKitAssistantMessage .copilotKitMarkdown strong:not(.copilotKitCodeBlock *),
.copilotKitAssistantMessage .copilotKitMarkdown em:not(.copilotKitCodeBlock *),
.copilotKitAssistantMessage .copilotKitMarkdown b:not(.copilotKitCodeBlock *),
.copilotKitAssistantMessage .copilotKitMarkdown li:not(.copilotKitCodeBlock *),
.copilotKitAssistantMessage .copilotKitMarkdown ul:not(.copilotKitCodeBlock *),
.copilotKitAssistantMessage .copilotKitMarkdown ol:not(.copilotKitCodeBlock *) {
  color: #1f2937 !important;
}

/* 针对emoji图标的特殊处理 */
.copilotKitMarkdownElement i[class*="fas"],
.copilotKitMarkdownElement i[class*="fa-"] {
  color: #6b7280 !important; /* 稍微浅一些的灰色用于图标 */
}

/* 强调文本样式 */
.copilotKitMarkdownElement strong,
.copilotKitMarkdownElement b {
  color: #111827 !important; /* 更深的颜色用于强调 */
  font-weight: 600;
}

/* 列表样式优化 */
.copilotKitMarkdownElement ul,
.copilotKitMarkdownElement ol {
  margin: 0.5em 0;
  padding-left: 1.5em;
}

.copilotKitMarkdownElement li {
  margin: 0.25em 0;
  color: #1f2937 !important;
}

/* 嵌套列表 */
.copilotKitMarkdownElement li ul,
.copilotKitMarkdownElement li ol,
.copilotKitMarkdownElement li li {
  color: #1f2937 !important;
}

/* 代码块工具栏样式修复 */
.copilotKitCodeBlockToolbar {
  background: #f3f4f6 !important;
  border-bottom: 1px solid #e5e7eb !important;
}

.copilotKitCodeBlockToolbarLanguage {
  color: #374151 !important; /* 深灰色用于语言标签 */
  font-size: 12px;
  font-weight: 500;
}

.copilotKitCodeBlockToolbarButton {
  color: #6b7280 !important; /* 中等灰色用于按钮 */
  background: transparent !important;
  border: none !important;
  padding: 4px !important;
  border-radius: 4px !important;
  cursor: pointer;
}

.copilotKitCodeBlockToolbarButton:hover {
  background: #e5e7eb !important;
  color: #374151 !important;
}

.copilotKitCodeBlockToolbarButton svg {
  color: currentColor !important;
  stroke: currentColor !important;
}

/* 代码块内容保护 - 强制使用内联样式，不被外部CSS规则覆盖 */
.copilotKitCodeBlock,
.copilotKitCodeBlock *,
.copilotKitCodeBlock div,
.copilotKitCodeBlock code,
.copilotKitCodeBlock pre,
.copilotKitCodeBlock span {
  color: unset !important; /* 使用原始内联样式 */
  background: unset !important; /* 保护背景色 */
}

/* 更强的保护：直接使用样式属性 */
.copilotKitCodeBlock [style],
.copilotKitCodeBlock [style*="color"],
.copilotKitCodeBlock [style*="background"] {
  color: unset !important;
  background: unset !important;
}

/* 最强保护：针对具体的代码块元素 */
div[style*="background: rgb(30, 30, 30)"],
div[style*="background: rgb(30, 30, 30)"] *,
code[style*="color: rgb(212, 212, 212)"],
code[style*="color: rgb(212, 212, 212)"] *,
span.token[style*="color"] {
  color: unset !important;
  background: unset !important;
}

/* 深色模式下的代码块工具栏 */
@media (prefers-color-scheme: dark) {
  .copilotKitCodeBlockToolbar {
    background: #374151 !important;
    border-bottom: 1px solid #4b5563 !important;
  }
  
  .copilotKitCodeBlockToolbarLanguage {
    color: #d1d5db !important;
  }
  
  .copilotKitCodeBlockToolbarButton {
    color: #9ca3af !important;
  }
  
  .copilotKitCodeBlockToolbarButton:hover {
    background: #4b5563 !important;
    color: #f3f4f6 !important;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .copilotKitMarkdownElement:not(.copilotKitCodeBlock):not(.copilotKitCodeBlock *),
  .copilotKitMarkdownElement p:not(.copilotKitCodeBlock *),
  .copilotKitMarkdownElement span:not(.copilotKitCodeBlock *),
  .copilotKitMarkdownElement div:not(.copilotKitCodeBlock *),
  .copilotKitMarkdownElement strong:not(.copilotKitCodeBlock *),
  .copilotKitMarkdownElement em:not(.copilotKitCodeBlock *),
  .copilotKitMarkdownElement b:not(.copilotKitCodeBlock *),
  .copilotKitMarkdownElement i:not([class*="fa"]):not(.copilotKitCodeBlock *),
  .copilotKitMarkdownElement li:not(.copilotKitCodeBlock *),
  .copilotKitMarkdownElement ul:not(.copilotKitCodeBlock *),
  .copilotKitMarkdownElement ol:not(.copilotKitCodeBlock *),
  .copilotKitAssistantMessage .copilotKitMarkdown:not(.copilotKitCodeBlock),
  .copilotKitAssistantMessage .copilotKitMarkdown p:not(.copilotKitCodeBlock *),
  .copilotKitAssistantMessage .copilotKitMarkdown span:not(.copilotKitCodeBlock *),
  .copilotKitAssistantMessage .copilotKitMarkdown div:not(.copilotKitCodeBlock *),
  .copilotKitAssistantMessage .copilotKitMarkdown strong:not(.copilotKitCodeBlock *),
  .copilotKitAssistantMessage .copilotKitMarkdown em:not(.copilotKitCodeBlock *),
  .copilotKitAssistantMessage .copilotKitMarkdown b:not(.copilotKitCodeBlock *),
  .copilotKitAssistantMessage .copilotKitMarkdown li:not(.copilotKitCodeBlock *),
  .copilotKitAssistantMessage .copilotKitMarkdown ul:not(.copilotKitCodeBlock *),
  .copilotKitAssistantMessage .copilotKitMarkdown ol:not(.copilotKitCodeBlock *) {
    color: #f3f4f6 !important; /* 深色模式下的浅色文字 */
  }
  
  .copilotKitMarkdownElement strong,
  .copilotKitMarkdownElement b {
    color: #ffffff !important; /* 深色模式下强调文本用白色 */
  }
  
  .copilotKitMarkdownElement i[class*="fas"],
  .copilotKitMarkdownElement i[class*="fa-"] {
    color: #9ca3af !important; /* 深色模式下的图标颜色 */
  }
}

/* Loading spinner动画 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.animate-spin {
  animation: spin 1s linear infinite;
} 