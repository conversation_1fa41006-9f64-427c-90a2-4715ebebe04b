// YNNX AI Platform - 统一API配置
// 集中管理所有API地址，避免硬编码

/**
 * 环境检测 - 判断是否在Node.js环境中运行
 */
const isNodeEnvironment = () => {
  return typeof process !== 'undefined' && process.env && typeof import.meta === 'undefined' || !import.meta.env;
};

/**
 * 获取环境变量的兼容函数
 */
const getEnvVar = (key, fallback = undefined) => {
  if (isNodeEnvironment()) {
    return process.env[key] || fallback;
  } else {
    return import.meta.env[key] || fallback;
  }
};

/**
 * API配置管理
 * 优先级：环境变量 > 默认配置
 */
const API_CONFIG = {
  // 开发环境默认配置
  DEVELOPMENT: {
    LITELLM_API_BASE: getEnvVar('VITE_DEV_LITELLM_API_BASE', 'http://192.168.1.3:4000'),
    LDAP_API_URL: getEnvVar('VITE_DEV_LDAP_API_URL', 'http://192.168.1.3:3002')
  },
  
  // 生产环境默认配置（应通过环境变量覆盖）
  PRODUCTION: {
    LITELLM_API_BASE: getEnvVar('VITE_PROD_LITELLM_API_BASE', 'http://192.168.1.3:4000'),
    LDAP_API_URL: getEnvVar('VITE_PROD_LDAP_API_URL', 'http://192.168.1.3:3002')
  }
};

// 获取当前环境
const getEnvironment = () => {
  return getEnvVar('NODE_ENV', 'development');
};

// 获取API配置
const getApiConfig = () => {
  const env = getEnvironment();
  let defaults;
  
  // 根据环境选择合适的默认配置
  if (env === 'production') {
    defaults = API_CONFIG.PRODUCTION;
  } else if (env === 'test' || getEnvVar('VITE_API_MODE') === 'test') {
    defaults = API_CONFIG.TEST;
  } else {
    defaults = API_CONFIG.DEVELOPMENT;
  }
  
  return {
    LITELLM_API_BASE: getEnvVar('VITE_LITELLM_API_BASE') || getEnvVar('LITELLM_API_BASE') || defaults.LITELLM_API_BASE,
    LDAP_API_URL: getEnvVar('VITE_LDAP_API_URL') || getEnvVar('LDAP_API_URL') || defaults.LDAP_API_URL
  };
};

// 导出配置
export const API_ENDPOINTS = getApiConfig();

// 便捷方法
export const getLiteLLMApiBase = () => API_ENDPOINTS.LITELLM_API_BASE;
export const getLdapApiUrl = () => API_ENDPOINTS.LDAP_API_URL;

// 调试信息（仅开发环境）
if (!isNodeEnvironment() && getEnvVar('DEV')) {
  console.log('🔧 API配置信息:', {
    environment: getEnvironment(),
    endpoints: API_ENDPOINTS,
    isNodeEnv: isNodeEnvironment()
  });
} 