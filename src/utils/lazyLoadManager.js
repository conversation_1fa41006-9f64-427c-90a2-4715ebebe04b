// YNNX AI Platform - 懒加载管理器
// 管理组件和资源的懒加载，优化首屏加载性能

/**
 * 懒加载组件映射
 * 动态导入非首屏组件，减少初始包体积
 */
const lazyComponents = {
  // 主要功能组件
  'ai-assistant': () => import('../components/AIAssistantSection'),
  'api-key': () => import('../components/APIKeySection'),
  'downloads': () => import('../components/DownloadsSection'),
  'documentation': () => import('../components/DocumentationSection'),
  'news': () => import('../components/NewsSection'),
  
  // 模态框组件
  'login': () => import('../components/LoginModal'),
  
  // 工具组件
  'browser-compatibility': () => import('../components/BrowserCompatibilityCheck'),
  'error-boundary': () => import('../components/ErrorBoundary'),
  'preloader': () => import('../components/ResourcePreloader'),
  
  // 导航组件
  'navbar': () => import('../components/Navbar'),
  'footer': () => import('../components/Footer')
};

/**
 * 懒加载资源映射
 * 按需加载样式、字体等静态资源
 */
const lazyResources = {
  // 字体资源
  'fontawesome': () => import('../assets/fonts/fontawesome.css'),
  'custom-fonts': () => import('../assets/fonts/optimized-fonts.css'),
  
  // 工具样式
  'animations': () => import('../styles/animations.css'),
  'utilities': () => import('../styles/utilities.css')
};

/**
 * 预加载重要组件
 * 在空闲时间预加载可能用到的组件
 */
export const preloadComponents = async (componentKeys = []) => {
  const defaultPreload = ['login', 'api-key']; // 默认预加载的组件
  const toPreload = componentKeys.length > 0 ? componentKeys : defaultPreload;
  
  return Promise.all(
    toPreload.map(async key => {
      if (lazyComponents[key]) {
        try {
          await lazyComponents[key]();
          console.log(`✅ 预加载组件: ${key}`);
        } catch (error) {
          console.warn(`⚠️ 预加载组件失败: ${key}`, error);
        }
      }
    })
  );
};

/**
 * 动态加载组件
 */
export const loadComponent = async (componentKey) => {
  if (lazyComponents[componentKey]) {
    try {
      const component = await lazyComponents[componentKey]();
      return component.default || component;
    } catch (error) {
      console.error(`❌ 加载组件失败: ${componentKey}`, error);
      throw error;
    }
  }
  
  throw new Error(`组件不存在: ${componentKey}`);
};

/**
 * 动态加载资源
 */
export const loadResource = async (resourceKey) => {
  if (lazyResources[resourceKey]) {
    try {
      await lazyResources[resourceKey]();
      console.log(`✅ 加载资源: ${resourceKey}`);
    } catch (error) {
      console.error(`❌ 加载资源失败: ${resourceKey}`, error);
      throw error;
    }
  }
};

/**
 * 批量预加载资源
 */
export const preloadResources = async (resourceKeys = []) => {
  const defaultResources = ['fontawesome']; // 默认预加载的资源
  const toPreload = resourceKeys.length > 0 ? resourceKeys : defaultResources;
  
  return Promise.all(
    toPreload.map(key => loadResource(key).catch(console.warn))
  );
};

/**
 * 检查组件是否可用
 */
export const isComponentAvailable = (componentKey) => {
  return Boolean(lazyComponents[componentKey]);
};

/**
 * 获取所有可用组件列表
 */
export const getAvailableComponents = () => {
  return Object.keys(lazyComponents);
};

/**
 * 懒加载管理器主类
 */
class LazyLoadManager {
  constructor() {
    this.loadedComponents = new Set();
    this.loadedResources = new Set();
    this.loadingPromises = new Map();
  }
  
  /**
   * 加载组件（带缓存）
   */
  async loadComponent(componentKey) {
    if (this.loadedComponents.has(componentKey)) {
      return; // 已加载，直接返回
    }
    
    // 避免重复加载
    if (this.loadingPromises.has(componentKey)) {
      return this.loadingPromises.get(componentKey);
    }
    
    const loadPromise = loadComponent(componentKey)
      .then(component => {
        this.loadedComponents.add(componentKey);
        this.loadingPromises.delete(componentKey);
        return component;
      })
      .catch(error => {
        this.loadingPromises.delete(componentKey);
        throw error;
      });
    
    this.loadingPromises.set(componentKey, loadPromise);
    return loadPromise;
  }
  
  /**
   * 预加载核心组件
   */
  async preloadCore() {
    const coreComponents = ['login', 'api-key', 'ai-assistant'];
    
    // 使用 requestIdleCallback 在浏览器空闲时预加载
    if (window.requestIdleCallback) {
      window.requestIdleCallback(() => {
        preloadComponents(coreComponents);
      });
    } else {
      // 降级方案：延迟预加载
      setTimeout(() => {
        preloadComponents(coreComponents);
      }, 2000);
    }
  }
  
  /**
   * 清理缓存
   */
  clearCache() {
    this.loadedComponents.clear();
    this.loadedResources.clear();
    this.loadingPromises.clear();
  }
}

// 创建全局实例
const lazyLoadManager = new LazyLoadManager();

export default lazyLoadManager; 