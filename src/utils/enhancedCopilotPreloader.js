/**
 * 增强版CopilotKit预加载器
 * 确保在用户与AI交互前，所有文档内容和页面信息都已准备就绪
 */

import enhancedDocumentService from '../services/documentService';
import pageContentCollector from '../services/pageContentCollector';

class EnhancedCopilotPreloader {
  constructor() {
    this.preloadStatus = {
      documentsLoaded: false,
      pageContentCollected: false,
      userTrackingSetup: false,
      errorLoggingSetup: false,
      startTime: null,
      endTime: null,
      errors: []
    };
    
    this.preloadPromise = null;
    this.retryCount = 0;
    this.maxRetries = 3;
  }

  /**
   * 开始预加载过程
   */
  async startPreload() {
    if (this.preloadPromise) {
      return this.preloadPromise;
    }

    this.preloadStatus.startTime = Date.now();
    console.log('[CopilotKit Preloader] 开始预加载...');

    this.preloadPromise = this.executePreload();
    return this.preloadPromise;
  }

  /**
   * 执行预加载逻辑
   */
  async executePreload() {
    const steps = [
      { name: '预加载文档库', fn: this.preloadDocuments.bind(this) },
      { name: '初始化页面内容收集', fn: this.initializePageCollection.bind(this) },
      { name: '设置用户交互追踪', fn: this.setupUserTracking.bind(this) },
      { name: '设置错误日志记录', fn: this.setupErrorLogging.bind(this) },
      { name: '验证系统状态', fn: this.validateSystemStatus.bind(this) }
    ];

    for (const step of steps) {
      try {
        console.log(`[CopilotKit Preloader] 执行: ${step.name}`);
        await step.fn();
        console.log(`[CopilotKit Preloader] ✓ ${step.name} 完成`);
      } catch (error) {
        console.error(`[CopilotKit Preloader] ✗ ${step.name} 失败:`, error);
        this.preloadStatus.errors.push({
          step: step.name,
          error: error.message,
          timestamp: Date.now()
        });

        // 对于关键步骤，进行重试
        if (['预加载文档库', '初始化页面内容收集'].includes(step.name) && this.retryCount < this.maxRetries) {
          this.retryCount++;
          console.log(`[CopilotKit Preloader] 重试 ${step.name} (${this.retryCount}/${this.maxRetries})`);
          await this.delay(1000 * this.retryCount); // 递增延迟
          await step.fn(); // 重试
        }
      }
    }

    this.preloadStatus.endTime = Date.now();
    const duration = this.preloadStatus.endTime - this.preloadStatus.startTime;
    
    console.log(`[CopilotKit Preloader] 预加载完成，耗时: ${duration}ms`);
    this.logPreloadSummary();
    
    return this.preloadStatus;
  }

  /**
   * 预加载文档库
   */
  async preloadDocuments() {
    try {
      const stats = await enhancedDocumentService.preloadAllDocuments();
      
      if (stats && stats.totalDocuments > 0) {
        this.preloadStatus.documentsLoaded = true;
        console.log(`[CopilotKit Preloader] 文档库加载完成: ${stats.totalDocuments} 个文档, ${stats.totalWords} 个词`);
      } else {
        throw new Error('文档库为空或加载失败');
      }
    } catch (error) {
      console.error('[CopilotKit Preloader] 文档预加载失败:', error);
      throw error;
    }
  }

  /**
   * 初始化页面内容收集
   */
  async initializePageCollection() {
    try {
      // 确保页面内容收集器已经启动
      if (!pageContentCollector.isCollecting) {
        pageContentCollector.startCollection();
      }

      // 等待初始内容收集完成
      await this.waitForPageContentReady();
      
      const content = pageContentCollector.getCurrentContent();
      if (content && content.timestamp) {
        this.preloadStatus.pageContentCollected = true;
        console.log(`[CopilotKit Preloader] 页面内容收集完成: ${content.structure.sections.length} 个区域`);
      } else {
        throw new Error('页面内容收集失败');
      }
    } catch (error) {
      console.error('[CopilotKit Preloader] 页面内容收集初始化失败:', error);
      throw error;
    }
  }

  /**
   * 等待页面内容准备就绪
   */
  async waitForPageContentReady(timeout = 5000) {
    const startTime = Date.now();
    
    return new Promise((resolve, reject) => {
      const checkReady = () => {
        const content = pageContentCollector.getCurrentContent();
        
        if (content && content.timestamp) {
          resolve(content);
          return;
        }
        
        if (Date.now() - startTime > timeout) {
          reject(new Error('页面内容收集超时'));
          return;
        }
        
        setTimeout(checkReady, 100);
      };
      
      checkReady();
    });
  }

  /**
   * 设置用户交互追踪
   */
  async setupUserTracking() {
    try {
      // 检查是否已经设置了用户交互追踪
      const stats = pageContentCollector.getStats();
      
      if (stats.interactionTypes && stats.interactionTypes.length > 0) {
        this.preloadStatus.userTrackingSetup = true;
        console.log(`[CopilotKit Preloader] 用户交互追踪已激活: ${stats.interactionTypes.join(', ')}`);
      } else {
        // 触发一次交互来确保追踪正常
        document.dispatchEvent(new Event('click'));
        
        // 等待一小段时间让事件处理完成
        await this.delay(100);
        
        const updatedStats = pageContentCollector.getStats();
        if (updatedStats.interactionTypes.length > 0) {
          this.preloadStatus.userTrackingSetup = true;
        } else {
          throw new Error('用户交互追踪设置失败');
        }
      }
    } catch (error) {
      console.error('[CopilotKit Preloader] 用户交互追踪设置失败:', error);
      // 非关键错误，不抛出异常
      this.preloadStatus.userTrackingSetup = false;
    }
  }

  /**
   * 设置错误日志记录
   */
  async setupErrorLogging() {
    try {
      // 测试错误日志是否正常工作
      const initialErrorCount = pageContentCollector.getRecentErrors().length;
      
      // 触发一个测试错误
      pageContentCollector.logError('preloader_test', { 
        message: 'Preloader error logging test',
        timestamp: Date.now()
      });
      
      // 等待错误记录
      await this.delay(100);
      
      const updatedErrorCount = pageContentCollector.getRecentErrors().length;
      
      if (updatedErrorCount > initialErrorCount) {
        this.preloadStatus.errorLoggingSetup = true;
        console.log('[CopilotKit Preloader] 错误日志记录已激活');
      } else {
        throw new Error('错误日志记录测试失败');
      }
    } catch (error) {
      console.error('[CopilotKit Preloader] 错误日志设置失败:', error);
      // 非关键错误，不抛出异常
      this.preloadStatus.errorLoggingSetup = false;
    }
  }

  /**
   * 验证系统状态
   */
  async validateSystemStatus() {
    try {
      const validations = [
        {
          name: '文档服务状态',
          check: () => enhancedDocumentService.getDocumentStats(),
          validator: (result) => result && result.totalDocuments > 0
        },
        {
          name: '页面收集器状态',
          check: () => pageContentCollector.getStats(),
          validator: (result) => result && result.isCollecting
        },
        {
          name: '页面内容可用性',
          check: () => pageContentCollector.getCurrentContent(),
          validator: (result) => result && result.timestamp
        }
      ];

      for (const validation of validations) {
        try {
          const result = await validation.check();
          if (!validation.validator(result)) {
            throw new Error(`${validation.name} 验证失败`);
          }
          console.log(`[CopilotKit Preloader] ✓ ${validation.name} 验证通过`);
        } catch (error) {
          console.error(`[CopilotKit Preloader] ✗ ${validation.name} 验证失败:`, error);
          this.preloadStatus.errors.push({
            step: validation.name,
            error: error.message,
            timestamp: Date.now()
          });
        }
      }
    } catch (error) {
      console.error('[CopilotKit Preloader] 系统状态验证失败:', error);
      throw error;
    }
  }

  /**
   * 延迟工具函数
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 记录预加载总结
   */
  logPreloadSummary() {
    const duration = this.preloadStatus.endTime - this.preloadStatus.startTime;
    const successCount = Object.values(this.preloadStatus).filter(v => v === true).length;
    const totalSteps = 4; // documentsLoaded, pageContentCollected, userTrackingSetup, errorLoggingSetup

    console.log(`
╔════════════════════════════════════════════════════════════╗
║                CopilotKit 预加载总结                        ║
╠════════════════════════════════════════════════════════════╣
║ 总耗时: ${duration}ms                                       ║
║ 成功步骤: ${successCount}/${totalSteps}                     ║
║ 错误数量: ${this.preloadStatus.errors.length}              ║
║                                                            ║
║ 状态详情:                                                   ║
║ • 文档库: ${this.preloadStatus.documentsLoaded ? '✓' : '✗'} ║
║ • 页面收集: ${this.preloadStatus.pageContentCollected ? '✓' : '✗'} ║
║ • 用户追踪: ${this.preloadStatus.userTrackingSetup ? '✓' : '✗'} ║
║ • 错误日志: ${this.preloadStatus.errorLoggingSetup ? '✓' : '✗'} ║
╚════════════════════════════════════════════════════════════╝
    `);

    if (this.preloadStatus.errors.length > 0) {
      console.log('预加载错误详情:');
      this.preloadStatus.errors.forEach((error, index) => {
        console.log(`${index + 1}. ${error.step}: ${error.error}`);
      });
    }
  }

  /**
   * 获取预加载状态
   */
  getStatus() {
    return {
      ...this.preloadStatus,
      isComplete: this.isPreloadComplete(),
      duration: this.preloadStatus.endTime ? 
        this.preloadStatus.endTime - this.preloadStatus.startTime : null
    };
  }

  /**
   * 检查预加载是否完成
   */
  isPreloadComplete() {
    return this.preloadStatus.documentsLoaded && 
           this.preloadStatus.pageContentCollected &&
           this.preloadStatus.endTime !== null;
  }

  /**
   * 获取系统健康状态
   */
  async getSystemHealth() {
    try {
      const documentStats = await enhancedDocumentService.getDocumentStats();
      const pageStats = pageContentCollector.getStats();
      const pageContent = pageContentCollector.getCurrentContent();

      return {
        timestamp: new Date().toISOString(),
        overall: this.isPreloadComplete() && this.preloadStatus.errors.length === 0 ? 'healthy' : 'warning',
        services: {
          documentService: {
            status: documentStats ? 'healthy' : 'error',
            documentsCount: documentStats?.totalDocuments || 0,
            wordsCount: documentStats?.totalWords || 0
          },
          pageCollector: {
            status: pageStats.isCollecting ? 'healthy' : 'error',
            lastUpdate: pageStats.lastUpdate,
            interactionTypes: pageStats.interactionTypes.length,
            totalInteractions: pageStats.totalInteractions
          },
          pageContent: {
            status: pageContent ? 'healthy' : 'error',
            sectionsCount: pageContent?.structure?.sections?.length || 0,
            lastCollected: pageContent?.timestamp
          }
        },
        preloadStatus: this.preloadStatus,
        recommendations: this.generateHealthRecommendations()
      };
    } catch (error) {
      console.error('[CopilotKit Preloader] 健康状态检查失败:', error);
      return {
        timestamp: new Date().toISOString(),
        overall: 'error',
        error: error.message,
        preloadStatus: this.preloadStatus
      };
    }
  }

  /**
   * 生成健康状态建议
   */
  generateHealthRecommendations() {
    const recommendations = [];

    if (!this.preloadStatus.documentsLoaded) {
      recommendations.push('文档库未完全加载，AI助手的文档搜索功能可能受限');
    }

    if (!this.preloadStatus.pageContentCollected) {
      recommendations.push('页面内容收集异常，AI助手的页面分析功能可能不准确');
    }

    if (!this.preloadStatus.userTrackingSetup) {
      recommendations.push('用户交互追踪未启用，AI助手无法了解用户行为');
    }

    if (!this.preloadStatus.errorLoggingSetup) {
      recommendations.push('错误日志记录未启用，问题诊断能力受限');
    }

    if (this.preloadStatus.errors.length > 0) {
      recommendations.push(`预加载过程中出现 ${this.preloadStatus.errors.length} 个错误，建议刷新页面重试`);
    }

    if (recommendations.length === 0) {
      recommendations.push('系统运行良好，所有功能正常');
    }

    return recommendations;
  }

  /**
   * 重置预加载器
   */
  reset() {
    this.preloadStatus = {
      documentsLoaded: false,
      pageContentCollected: false,
      userTrackingSetup: false,
      errorLoggingSetup: false,
      startTime: null,
      endTime: null,
      errors: []
    };
    
    this.preloadPromise = null;
    this.retryCount = 0;
    
    console.log('[CopilotKit Preloader] 预加载器已重置');
  }

  /**
   * 手动触发预加载（用于调试）
   */
  async forcePreload() {
    console.log('[CopilotKit Preloader] 强制重新预加载...');
    this.reset();
    return await this.startPreload();
  }
}

// 创建全局实例
const enhancedCopilotPreloader = new EnhancedCopilotPreloader();

// 自动启动预加载
if (typeof window !== 'undefined') {
  // 确保DOM已加载
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      enhancedCopilotPreloader.startPreload();
    });
  } else {
    // DOM已就绪，立即开始预加载
    enhancedCopilotPreloader.startPreload();
  }

  // 添加全局调试接口
  window.copilotKitPreloader = enhancedCopilotPreloader;
  
  // 页面卸载前清理
  window.addEventListener('beforeunload', () => {
    console.log('[CopilotKit Preloader] 页面卸载，清理资源...');
  });
}

export default enhancedCopilotPreloader;
export { EnhancedCopilotPreloader }; 