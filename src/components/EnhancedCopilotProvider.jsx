import React, { useEffect, useState, useCallback, useRef } from 'react';
import { useCopilotReadable, useCopilotAction } from '@copilotkit/react-core';
import { Markdown } from '@copilotkit/react-ui';
import enhancedDocumentService from '../services/documentService';
import pageContentCollector from '../services/pageContentCollector';
import documentLoadingDiagnostic from '../utils/documentLoadingDiagnostic';
import appPreloader from '../services/AppPreloader';

/**
 * 增强版CopilotProvider组件
 * 实现全面的上下文感知功能，包括：
 * - 动态加载所有开发者文档
 * - 实时收集页面内容
 * - 智能内容搜索和检索
 * - 上下文感知的AI交互
 */
const EnhancedCopilotProvider = ({ user, children }) => {
  const [documentsLoaded, setDocumentsLoaded] = useState(false);
  const [pageContentLoaded, setPageContentLoaded] = useState(false);
  const [allDocuments, setAllDocuments] = useState({});
  const [currentPageContent, setCurrentPageContent] = useState(null);
  const [documentStats, setDocumentStats] = useState(null);
  const loadingRef = useRef(false);

  // 辅助方法：检测当前页面区域 - 移到组件开头
  const _detectCurrentSection = useCallback((headings) => {
    if (!headings || headings.length === 0) return null;

    const viewportHeight = window.innerHeight;
    const _scrollY = window.scrollY;

    // 找到最接近当前滚动位置的标题
    let currentSection = null;
    let minDistance = Infinity;

    headings.forEach(heading => {
      const element = document.getElementById(heading.id);
      if (element) {
        const rect = element.getBoundingClientRect();
        const distance = Math.abs(rect.top - viewportHeight * 0.2); // 20% from top

        if (distance < minDistance) {
          minDistance = distance;
          currentSection = heading.text;
        }
      }
    });

    return currentSection;
  }, []);

  // 使用预加载的数据初始化
  useEffect(() => {
    if (loadingRef.current) return;
    loadingRef.current = true;

    const initializeFromPreloader = async () => {
      try {
        // 检查预加载是否完成
        if (appPreloader.isComplete()) {
          // 直接使用预加载的数据
          const preloadedData = appPreloader.getPreloadedData();

          if (preloadedData.documents) {
            setAllDocuments(preloadedData.documents);
            setDocumentsLoaded(true);
          }

          if (preloadedData.pageContent) {
            setCurrentPageContent(preloadedData.pageContent);
            setPageContentLoaded(true);
          }

          if (preloadedData.documentStats) {
            setDocumentStats(preloadedData.documentStats);
          }

          console.log('增强版CopilotProvider使用预加载数据初始化完成');
        } else {
          // 预加载未完成，启动预加载
          console.log('启动预加载过程...');
          
          // 在后台启动预加载，不阻塞页面显示
          appPreloader.startPreload().then(() => {
            const preloadedData = appPreloader.getPreloadedData();

            setAllDocuments(preloadedData.documents || {});
            setCurrentPageContent(preloadedData.pageContent || null);
            setDocumentStats(preloadedData.documentStats || null);
            setDocumentsLoaded(!!preloadedData.documents);
            setPageContentLoaded(!!preloadedData.pageContent);

            console.log('增强版CopilotProvider后台预加载完成');
                     }).catch(error => {
             console.error('后台预加载失败:', error);
             // 降级处理
             initializeFallback();
           });
        }
      } catch (error) {
        console.error('初始化失败:', error);
        initializeFallback();
      } finally {
        loadingRef.current = false;
      }
    };

    const initializeFallback = async () => {
      try {
        console.log('开始降级初始化...');
        const [docs, pageContent, stats] = await Promise.allSettled([
          enhancedDocumentService.getAllDocumentsContent(),
          pageContentCollector.collectPageContent(),
          enhancedDocumentService.getDocumentStats()
        ]);

        // 处理降级加载的结果
        if (docs.status === 'fulfilled') {
          setAllDocuments(docs.value || {});
          setDocumentsLoaded(true);
        } else {
          console.warn('文档服务降级失败:', docs.reason);
          setAllDocuments({});
          setDocumentsLoaded(false);
        }

        if (pageContent.status === 'fulfilled') {
          setCurrentPageContent(pageContent.value);
          setPageContentLoaded(true);
        } else {
          console.warn('页面内容收集降级失败:', pageContent.reason);
          setCurrentPageContent(null);
          setPageContentLoaded(false);
        }

        if (stats.status === 'fulfilled') {
          setDocumentStats(stats.value);
        } else {
          console.warn('文档统计降级失败:', stats.reason);
          setDocumentStats(null);
        }

        console.log('增强版CopilotProvider降级初始化完成');
      } catch (fallbackError) {
        console.error('降级初始化也失败:', fallbackError);
        // 最终降级：设置基本的空状态
        console.log('使用最终降级模式');
        setAllDocuments({});
        setCurrentPageContent(null);
        setDocumentStats(null);
        setDocumentsLoaded(false);
        setPageContentLoaded(false);
      }
    };

    initializeFromPreloader();
  }, []);

  // 监听页面变化，更新内容
  useEffect(() => {
    const handlePageChange = async () => {
      if (pageContentLoaded) {
        try {
          const updatedContent = await pageContentCollector.collectPageContent();
          setCurrentPageContent(updatedContent);
        } catch (error) {
          console.error('更新页面内容失败:', error);
        }
      }
    };

    // 监听路由变化
    window.addEventListener('popstate', handlePageChange);
    window.addEventListener('hashchange', handlePageChange);

    return () => {
      window.removeEventListener('popstate', handlePageChange);
      window.removeEventListener('hashchange', handlePageChange);
    };
  }, [pageContentLoaded]);

  // 提供最小化的平台信息 - 减少载荷大小
  useCopilotReadable({
    description: "YNNX AI开发平台基本信息",
    value: {
      platform: "YNNX AI开发平台 - 云南农信智能开发助手",
      userLoggedIn: !!user,
      capabilities: {
        documentSearch: documentsLoaded,
        pageAnalysis: pageContentLoaded
      }
    }
  });

  // 提供简化的文档库信息 - 减少数据量，提高回复质量
  useCopilotReadable({
    description: "文档库状态和可用文档概览",
    value: documentsLoaded ? {
      libraryStatus: "完全加载",
      totalDocuments: documentStats?.totalDocuments || 0,
      availableCategories: Object.keys(allDocuments),
      searchCapable: true,
      lastUpdated: documentStats?.lastUpdated
    } : {
      libraryStatus: "基础模式",
      availableCategories: ["cline", "jetbrains", "faq"],
      basicDocs: [
        "Cline插件安装指南",
        "JetBrains AI助手安装指南", 
        "常见问题解答"
      ],
      searchCapable: false
    }
  });

  // 提供简化的页面状态信息
  useCopilotReadable({
    description: "当前页面基本信息",
    value: currentPageContent ? {
      pageTitle: currentPageContent.title,
      url: currentPageContent.url,
      hasInteractiveElements: Object.values(currentPageContent.interactiveElements || {}).flat().length > 0,
      hasDownloads: (currentPageContent.appState?.downloadContent?.totalDownloads || 0) > 0,
      isLoading: currentPageContent.applicationState?.loading?.isLoading || false
    } : {
      status: "正在收集页面内容..."
    }
  });

  // 简化的帮助Action  
  useCopilotAction({
    name: "quickHelp",
    description: "为用户提供快速帮助和指导",
    parameters: [],
    handler: async () => {
      return {
        message: "我可以帮您搜索文档、分析页面功能、检查系统状态。请直接告诉我您需要什么帮助！",
        availableFeatures: [
          "文档搜索：查找安装指南和配置教程",
          "页面分析：了解当前页面功能",
          "状态检查：检查系统运行状况"
        ]
      };
    }
  });

  // 文档库状态检查Action
  useCopilotAction({
    name: "getDocumentLibraryStatus",
    description: "获取文档库的当前状态和可用内容概览",
    parameters: [],
    handler: async () => {
      try {
        // 首先检查文档服务的状态
        const stats = await enhancedDocumentService.getDocumentStats();

        if (!stats.canSearch) {
          return {
            status: stats.loadingStatus.status,
            message: stats.loadingStatus.message,
            canSearch: false,
            totalDocuments: stats.totalDocuments,
            isLoading: stats.isLoading,
            recommendation: "请等待文档库加载完成后再进行搜索"
          };
        }

        return {
          status: "ready",
          message: "文档库已加载完成，可以进行搜索",
          canSearch: true,
          totalDocuments: stats.totalDocuments,
          totalWords: stats.totalWords,
          totalSections: stats.totalSections,
          categoriesCount: stats.categoriesCount,
          categories: Object.keys(stats.categories),
          recommendation: "文档库已就绪，您可以搜索任何相关内容"
        };
      } catch (error) {
        console.error('获取文档库状态失败:', error);
        return {
          status: "error",
          message: "无法获取文档库状态",
          canSearch: false,
          error: error.message,
          recommendations: ["请刷新页面或检查网络连接"],
          userActions: [
            { type: 'refresh', text: '刷新页面', description: '重新加载文档库' },
            { type: 'retry', text: '重试加载', description: '尝试重新加载文档' }
          ]
        };
      }
    }
  });

  // 简化的文档搜索Action
  useCopilotAction({
    name: "searchDocuments",
    description: "搜索安装指南、配置教程、常见问题等文档",
    parameters: [
      {
        name: "query",
        type: "string",
        description: "搜索关键词或问题",
        required: true
      }
    ],
    handler: async ({ query }) => {
      try {
        // 首先检查文档库状态
        const stats = await enhancedDocumentService.getDocumentStats();
        if (!stats.canSearch) {
          return {
            error: true,
            message: stats.loadingStatus.message,
            query: query,
            suggestion: "请等待文档库加载完成后再进行搜索"
          };
        }

        const searchOptions = {
          maxResults: 5
        };

        console.log('执行文档搜索:', { query, searchOptions });
        const results = await enhancedDocumentService.searchDocuments(query, searchOptions);
        console.log('搜索结果:', results);

        if (results.length === 0) {
          return {
            query: query,
            resultsCount: 0,
            results: [],
            message: "未找到相关内容",
            suggestion: "尝试使用不同的关键词或检查拼写"
          };
        }

        return {
          query: query,
          resultsCount: results.length,
          results: results.map(result => ({
            document: {
              title: result.document?.title || result.filePath,
              description: result.document?.description || result.summary,
              category: result.category,
              tags: result.document?.tags || []
            },
            relevanceScore: result.relevanceScore,
            matches: result.matches.map(match => ({
              type: match.type,
              section: match.section || match.content,
              content: match.content,
              context: match.context,
              lineNumber: match.lineNumber
            }))
          }))
        };
      } catch (error) {
        console.error('文档搜索失败:', error);
        return {
          error: true,
          message: error.message || "搜索失败，请稍后重试",
          query: query,
          suggestion: error.message?.includes('加载') ? "请刷新页面重试" : "请检查网络连接或稍后重试"
        };
      }
    },
    render: ({ status, args, result }) => {
      if (status === 'executing') {
        return (
          <div className="flex items-center gap-2 text-blue-600 bg-blue-50 p-3 rounded-lg">
            <div className="animate-spin w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full"></div>
            🔍 正在搜索文档："{args.query}"...
          </div>
        );
      }
      
      if (status === 'complete' && result) {
        if (result.error) {
          return (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="font-medium text-red-800">❌ 搜索失败</div>
              <div className="text-sm text-red-700">{result.message}</div>
            </div>
          );
        }

        return (
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 space-y-4">
            <div className="font-medium text-gray-800">
              📚 文档搜索结果 ({result.resultsCount} 个匹配项)
            </div>
            
            {result.results.map((item, index) => (
              <div key={`doc-${index}-${item.document?.id || item.document?.title || index}`} className="bg-white border border-gray-200 rounded p-3 space-y-2">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="font-medium text-gray-900">{item.document?.title || '未知文档'}</div>
                    <div className="text-sm text-gray-600">{item.document?.description || '无描述'}</div>
                    <div className="text-xs text-gray-500">
                      分类: {item.document?.category || '未分类'} | 相关性: {item.relevanceScore || 0}
                    </div>
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {item.document?.tags?.map((tag, tagIndex) => (
                      <span key={`tag-${index}-${tagIndex}-${tag}`} className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded">
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
                
                {item.matches.length > 0 && (
                  <div className="border-t pt-2">
                    <div className="text-sm font-medium text-gray-700">匹配内容:</div>
                    {item.matches?.slice(0, 2).map((match, matchIndex) => (
                      <div key={`match-${index}-${matchIndex}-${match.section || matchIndex}`} className="text-xs text-gray-600 mt-1">
                        <span className="font-medium">[{match.section}]</span> {match.content?.substring(0, 100)}...
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        );
      }
      
      return null;
    }
  });

  // 智能问题路由Action（新增）
  useCopilotAction({
    name: "smartQuestionRouter",
    description: "当用户询问'工具下载有什么'、'页面上有什么'、'这里显示什么内容'、'当前页面有哪些下载'、'有什么可以下载的工具'、'页面包含什么功能'等关于当前页面具体内容的问题时，使用此功能智能分析页面内容并提供准确答案。特别适用于询问页面上的下载工具、软件、文档等具体内容。",
    parameters: [
      {
        name: "question",
        type: "string",
        description: "用户关于页面内容的具体问题，例如：'工具下载有什么'、'页面上有哪些下载内容'、'这里显示了什么工具'等",
        required: true
      }
    ],
    handler: async ({ question }) => {
      console.log('[Smart Router] 开始处理问题:', question);
      const startTime = performance.now();

      try {
        // 分析问题类型
        console.log('[Smart Router] 分析问题类型...');
        const questionType = _analyzeQuestionType(question);
        console.log('[Smart Router] 问题类型分析结果:', questionType);

        let result = {
          questionType: questionType.type,
          confidence: questionType.confidence,
          reasoning: questionType.reasoning,
          recommendedAction: questionType.recommendedAction
        };

        // 根据问题类型选择合适的处理方式
        if (questionType.type === 'page-content') {
          console.log('[Smart Router] 使用页面分析模式');
          const pageAnalysisStart = performance.now();
          const pageAnalysis = await pageContentCollector.collectPageContent();
          const pageAnalysisTime = performance.now() - pageAnalysisStart;
          console.log(`[Smart Router] 页面分析完成，耗时: ${pageAnalysisTime.toFixed(2)}ms`);

          result.source = 'page-analysis';
          result.data = _formatPageContentAnswer(pageAnalysis, question);
          console.log('[Smart Router] 页面内容格式化完成');

        } else if (questionType.type === 'documentation') {
          console.log('[Smart Router] 使用文档搜索模式');
          const searchResults = await enhancedDocumentService.searchDocuments(question, { maxResults: 5 });
          result.source = 'document-search';
          result.data = _formatDocumentAnswer(searchResults, question);

        } else if (questionType.type === 'hybrid') {
          console.log('[Smart Router] 使用混合模式');
          const [pageAnalysis, searchResults] = await Promise.all([
            pageContentCollector.collectPageContent(),
            enhancedDocumentService.searchDocuments(question, { maxResults: 3 })
          ]);

          result.source = 'hybrid';
          result.data = _formatHybridAnswer(pageAnalysis, searchResults, question);
        }

        const totalTime = performance.now() - startTime;
        console.log(`[Smart Router] 处理完成，总耗时: ${totalTime.toFixed(2)}ms`);
        console.log('[Smart Router] 返回结果:', result);

        return result;

      } catch (error) {
        const totalTime = performance.now() - startTime;
        console.error(`[Smart Router] 处理失败，耗时: ${totalTime.toFixed(2)}ms`, error);
        return {
          questionType: 'unknown',
          error: true,
          message: '问题分析失败，请尝试直接使用页面分析或文档搜索功能',
          fallbackSuggestions: [
            '尝试询问"当前页面有什么内容"进行页面分析',
            '尝试搜索具体的技术文档或使用指南'
          ]
        };
      }
    },
    render: ({ status, result }) => {
      if (status === 'executing') {
        return (
          <div className="flex items-center gap-2 text-purple-600 bg-purple-50 p-3 rounded-lg">
            <div className="animate-spin w-4 h-4 border-2 border-purple-500 border-t-transparent rounded-full"></div>
            🧠 正在智能分析问题类型...
          </div>
        );
      }

      if (status === 'complete' && result) {
        if (result.error) {
          return (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="font-medium text-red-800">❌ 智能路由失败</div>
              <div className="text-sm text-red-700 mt-1">{result.message}</div>
              {result.fallbackSuggestions && (
                <div className="mt-2">
                  <div className="text-sm font-medium text-red-800">建议:</div>
                  <ul className="text-sm text-red-700 mt-1 list-disc list-inside">
                    {result.fallbackSuggestions.map((suggestion, index) => (
                      <li key={`suggestion-${index}-${suggestion.substring(0, 10)}`}>{suggestion}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          );
        }

        return (
          <div className="bg-purple-50 border border-purple-200 rounded-lg p-4 space-y-4">
            <div className="font-medium text-purple-800">
              🧠 智能问题分析结果
            </div>

            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium text-gray-700">问题类型:</span>
                <span className="ml-2 text-purple-700">{result.questionType}</span>
              </div>
              <div>
                <span className="font-medium text-gray-700">置信度:</span>
                <span className="ml-2 text-purple-700">{Math.round(result.confidence * 100)}%</span>
              </div>
            </div>

            <div className="text-sm">
              <span className="font-medium text-gray-700">分析原因:</span>
              <div className="text-gray-600 mt-1">{result.reasoning}</div>
            </div>

            <div className="text-sm">
              <span className="font-medium text-gray-700">推荐操作:</span>
              <div className="text-purple-700 mt-1">{result.recommendedAction}</div>
            </div>

            {result.data && (
              <div className="border-t border-purple-200 pt-4">
                <div className="font-medium text-purple-800 mb-2">📊 分析结果</div>
                {_renderSmartRouterData(result)}
              </div>
            )}
          </div>
        );
      }

      return null;
    }
  });

  // 页面下载内容查询Action（专门处理下载相关问题）
  useCopilotAction({
    name: "queryPageDownloads",
    description: "当用户询问'工具下载有什么'、'有什么可以下载'、'页面上有哪些下载工具'、'这里有什么软件可以下载'等关于页面下载内容的问题时，专门分析当前页面的下载工具、软件和资源。",
    parameters: [
      {
        name: "query",
        type: "string",
        description: "用户关于下载内容的问题",
        required: false
      }
    ],
    handler: async ({ query = "页面下载内容" }) => {
      console.log('[Download Query] 开始查询页面下载内容:', query);
      const startTime = performance.now();

      try {
        // 添加延迟以避免与React渲染冲突
        await new Promise(resolve => setTimeout(resolve, 100));

        // 检查DOM是否可用
        if (!document || !document.body) {
          throw new Error('DOM未准备就绪');
        }

        // 使用try-catch包装页面分析，防止DOM操作错误
        let pageAnalysis;
        try {
          pageAnalysis = await pageContentCollector.collectPageContent();
        } catch (domError) {
          console.warn('[Download Query] DOM操作失败，使用简化分析:', domError.message);
          // 简化的页面分析，避免复杂的DOM操作
          pageAnalysis = {
            title: document.title || '未知页面',
            url: window.location.href,
            appState: {
              downloadContent: {
                hasDownloadSection: false,
                totalDownloads: 0,
                tools: [],
                software: [],
                documents: [],
                downloadButtons: [],
                downloadLinks: []
              }
            }
          };
        }

        const downloadContent = pageAnalysis.appState?.downloadContent || {};

        const result = {
          query: query,
          hasDownloads: downloadContent.hasDownloadSection || false,
          totalItems: downloadContent.totalDownloads || 0,
          downloadSummary: {
            tools: (downloadContent.tools || []).map(tool => ({
              name: tool?.name || '未知工具',
              description: tool?.description || '',
              version: tool?.version || '',
              platform: tool?.platform || ''
            })),
            software: (downloadContent.software || []).map(sw => ({
              name: sw?.name || '未知软件',
              description: sw?.description || '',
              version: sw?.version || ''
            })),
            documents: (downloadContent.documents || []).map(doc => ({
              name: doc?.name || '未知文档',
              type: doc?.type || '',
              size: doc?.size || ''
            })),
            downloadButtons: (downloadContent.downloadButtons || []).map(btn => ({
              text: btn?.text || '未知按钮',
              disabled: btn?.disabled || false
            })),
            downloadLinks: (downloadContent.downloadLinks || []).map(link => ({
              text: link?.text || '未知链接',
              fileType: link?.fileType || ''
            }))
          },
          pageInfo: {
            title: pageAnalysis.title || '未知页面',
            url: pageAnalysis.url || window.location.href
          }
        };

        const totalTime = performance.now() - startTime;
        console.log(`[Download Query] 查询完成，耗时: ${totalTime.toFixed(2)}ms`);
        console.log('[Download Query] 查询结果:', result);

        return result;

      } catch (error) {
        console.error('[Download Query] 查询失败:', error);
        return {
          query: query,
          error: true,
          message: `无法获取页面下载内容: ${error.message}`,
          hasDownloads: false,
          totalItems: 0,
          downloadSummary: {
            tools: [],
            software: [],
            documents: [],
            downloadButtons: [],
            downloadLinks: []
          }
        };
      }
    },
    render: ({ status, result }) => {
      if (status === 'executing') {
        return (
          <div className="flex items-center gap-2 text-blue-600 bg-blue-50 p-3 rounded-lg">
            <div className="animate-spin w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full"></div>
            🔍 正在分析页面下载内容...
          </div>
        );
      }

      if (status === 'complete' && result) {
        if (result.error) {
          return (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="text-red-800 font-medium mb-2">❌ 查询失败</div>
              <div className="text-red-700 text-sm">{result.message}</div>
            </div>
          );
        }

        return (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="text-green-800 font-medium mb-3">
              📦 页面下载内容分析结果
            </div>

            {result.hasDownloads ? (
              <div className="space-y-3">
                <div className="text-sm text-green-700">
                  <span className="font-medium">总计:</span> 找到 {result.totalItems} 个下载项目
                </div>

                {result.downloadSummary?.tools?.length > 0 && (
                  <div>
                    <div className="font-medium text-green-800 mb-2">🛠️ 开发工具 ({result.downloadSummary.tools.length}个)</div>
                    <div className="space-y-1">
                      {result.downloadSummary.tools.map((tool, index) => {
                        const toolKey = `tool-${tool?.name || 'unknown'}-${index}-${Date.now()}`;
                        return (
                          <div key={toolKey} className="text-sm text-green-700 bg-white p-2 rounded border">
                            <div className="font-medium">{tool?.name || '未知工具'}</div>
                            {tool?.description && <div className="text-xs text-gray-600">{tool.description}</div>}
                            {tool?.version && <div className="text-xs text-blue-600">版本: {tool.version}</div>}
                          </div>
                        );
                      })}
                    </div>
                  </div>
                )}

                {result.downloadSummary?.software?.length > 0 && (
                  <div>
                    <div className="font-medium text-green-800 mb-2">💻 软件应用 ({result.downloadSummary.software.length}个)</div>
                    <div className="space-y-1">
                      {result.downloadSummary.software.map((software, index) => {
                        const softwareKey = `software-${software?.name || 'unknown'}-${index}-${Date.now()}`;
                        return (
                          <div key={softwareKey} className="text-sm text-green-700 bg-white p-2 rounded border">
                            <div className="font-medium">{software?.name || '未知软件'}</div>
                            {software?.description && <div className="text-xs text-gray-600">{software.description}</div>}
                          </div>
                        );
                      })}
                    </div>
                  </div>
                )}

                {result.downloadSummary?.documents?.length > 0 && (
                  <div>
                    <div className="font-medium text-green-800 mb-2">📚 文档资料 ({result.downloadSummary.documents.length}个)</div>
                    <div className="space-y-1">
                      {result.downloadSummary.documents.map((doc, index) => {
                        const docKey = `doc-${doc?.name || 'unknown'}-${index}-${Date.now()}`;
                        return (
                          <div key={docKey} className="text-sm text-green-700 bg-white p-2 rounded border">
                            <div className="font-medium">{doc?.name || '未知文档'}</div>
                            {doc?.type && <div className="text-xs text-gray-600">类型: {doc.type}</div>}
                          </div>
                        );
                      })}
                    </div>
                  </div>
                )}

                {result.downloadSummary?.downloadButtons?.length > 0 && (
                  <div>
                    <div className="font-medium text-green-800 mb-2">🔘 下载按钮 ({result.downloadSummary.downloadButtons.length}个)</div>
                    <div className="flex flex-wrap gap-1">
                      {result.downloadSummary.downloadButtons.map((btn, index) => {
                        const btnKey = `btn-${btn?.text || 'unknown'}-${index}-${Date.now()}`;
                        return (
                          <span key={btnKey} className={`text-xs px-2 py-1 rounded ${btn?.disabled ? 'bg-gray-200 text-gray-500' : 'bg-blue-100 text-blue-700'}`}>
                            {btn?.text || '未知按钮'}
                          </span>
                        );
                      })}
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-sm text-green-700">
                当前页面暂无可下载的内容
              </div>
            )}

            <div className="mt-3 pt-3 border-t border-green-200">
              <div className="text-xs text-green-600">
                📍 页面: {result.pageInfo?.title || '未知页面'}
              </div>
            </div>
          </div>
        );
      }

      return null;
    }
  });

  // 智能页面分析Action（增强版）
  useCopilotAction({
    name: "analyzeCurrentPage",
    description: "当用户询问'当前页面有什么功能'、'这个页面如何使用'、'页面状态如何'、'有哪些可用操作'、'页面分析'等问题时，深度分析当前页面的内容、状态和可用操作。提供详细的功能介绍和使用指导。",
    handler: async () => {
      try {
        const content = await pageContentCollector.collectPageContent();
        
        return {
          pageInfo: {
            url: content.url,
            title: content.title,
            timestamp: content.timestamp,
            pageType: _detectPageType(content),
            primaryPurpose: _analyzePrimaryPurpose(content)
          },

          contentAnalysis: {
            structure: {
              sectionsCount: content.structure?.sections?.length || 0,
              headingsCount: content.structure?.headings?.length || 0,
              mainHeadings: content.structure?.headings?.filter(h => h.level <= 2).map(h => h.text) || [],
              navigationDepth: _calculateNavigationDepth(content)
            },

            interactivity: {
              totalInteractiveElements: Object.values(content.interactiveElements || {}).flat().length,
              buttonsByType: _categorizeButtons(content.interactiveElements?.buttons || []),
              formsAnalysis: _analyzeFormsInDetail(content.formData?.forms || []),
              linksAnalysis: _analyzeLinks(content.interactiveElements?.links || [])
            },

            functionality: {
              functionalAreas: content.appState?.functionalAreas || [],
              workflowState: content.appState?.workflowState || {},
              reactComponents: content.appState?.reactState?.componentCount || 0,
              stateManagement: content.appState?.reactState?.stateManagement || []
            },

            accessibility: content.appState?.accessibility || { score: 0, issues: [], recommendations: [] },

            downloadContent: content.appState?.downloadContent || {
              tools: [],
              software: [],
              documents: [],
              resources: [],
              downloadButtons: [],
              downloadLinks: [],
              categories: [],
              totalDownloads: 0,
              hasDownloadSection: false
            }
          },

          userGuidance: {
            quickActions: _generateQuickActions(content),
            stepByStepGuide: _generateStepByStepGuide(content),
            troubleshooting: _generateTroubleshooting(content),
            tips: _generateUsageTips(content)
          },

          currentState: {
            user: content.appState?.user || { loggedIn: false },
            workflow: content.appState?.workflowState || {},
            loading: _detectLoadingStates(content),
            errors: _detectErrors(content),
            notifications: _detectNotifications(content)
          },

          recommendations: _generateEnhancedRecommendations(content),

          nextSteps: _suggestNextSteps(content)
        };
      } catch (error) {
        console.error('页面分析失败:', error);
        return {
          error: true,
          message: "页面分析失败，请稍后重试"
        };
      }
    },
    render: ({ status, result }) => {
      if (status === 'executing') {
        return (
          <div className="flex items-center gap-2 text-blue-600 bg-blue-50 p-3 rounded-lg">
            <div className="animate-spin w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full"></div>
            🔍 正在分析当前页面...
          </div>
        );
      }
      
      if (status === 'complete' && result) {
        if (result.error) {
          return (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="font-medium text-red-800">❌ 分析失败</div>
              <div className="text-sm text-red-700">{result.message}</div>
            </div>
          );
        }

        return (
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 space-y-4">
            <div className="font-medium text-gray-800">
              🔍 页面分析结果: {result.pageInfo.title}
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-white border border-gray-200 rounded p-3">
                <div className="font-medium text-gray-700 mb-2">📊 内容统计</div>
                <div className="text-sm text-gray-600 space-y-1">
                  <div>区域数量: {result.contentAnalysis.sectionsCount}</div>
                  <div>标题数量: {result.contentAnalysis.headingsCount}</div>
                  <div>交互元素: {result.availableActions.buttons.length + result.availableActions.forms.length}</div>
                </div>
              </div>
              
              <div className="bg-white border border-gray-200 rounded p-3">
                <div className="font-medium text-gray-700 mb-2">⚡ 当前状态</div>
                <div className="text-sm text-gray-600 space-y-1">
                  <div>加载中: {result.currentState.isLoading ? '是' : '否'}</div>
                  <div>模态框: {result.currentState.hasModals ? '显示' : '隐藏'}</div>
                  <div>用户状态: {result.currentState.userLoggedIn ? '已登录' : '未登录'}</div>
                </div>
              </div>
            </div>

            {result.contentAnalysis.mainHeadings.length > 0 && (
              <div className="bg-white border border-gray-200 rounded p-3">
                <div className="font-medium text-gray-700 mb-2">📝 主要内容</div>
                <div className="text-sm text-gray-600">
                  {result.contentAnalysis.mainHeadings.join(' • ')}
                </div>
              </div>
            )}

            {result.recommendations.length > 0 && (
              <div className="bg-blue-50 border border-blue-200 rounded p-3">
                <div className="font-medium text-blue-800 mb-2">💡 建议操作</div>
                <div className="text-sm text-blue-700 space-y-1">
                  {result.recommendations?.map((rec, index) => (
                    <div key={`rec-${index}-${rec.substring(0, 10)}`}>• {rec}</div>
                  ))}
                </div>
              </div>
            )}
          </div>
        );
      }
      
      return null;
    }
  });

  // 继承原有的导航Action，但增强上下文感知
  useCopilotAction({
    name: "navigateToSection",
    description: "智能导航到平台的指定区域，支持上下文感知的导航建议",
    parameters: [
      {
        name: "target",
        type: "string",
        description: "目标区域，可以是英文ID或中文名称",
        required: true
      }
    ],
    handler: ({ target }) => {
      console.log(`[增强版导航] 导航请求: "${target}"`);
      
      // 扩展的映射表
      const sectionMapping = {
        // 首页相关
        '首页': 'home', '主页': 'home', 'home': 'home',
        
        // 功能特性相关  
        '功能': 'features', '功能特性': 'features', '功能介绍': 'features', 
        '平台功能': 'features', 'features': 'features',
        
        // AI助手相关
        'AI助手': 'ai-assistant', 'ai助手': 'ai-assistant', 'AI智能助手': 'ai-assistant',
        'ai智能助手': 'ai-assistant', '智能助手': 'ai-assistant', '助手': 'ai-assistant',
        'ai-assistant': 'ai-assistant',
        
        // API密钥相关
        'API密钥': 'api-key', 'api密钥': 'api-key', 'API密钥管理': 'api-key',
        'api密钥管理': 'api-key', '密钥': 'api-key', '密钥管理': 'api-key',
        'api-key': 'api-key',
        
        // 工具下载相关
        '工具下载': 'downloads', '下载': 'downloads', '工具': 'downloads',
        '插件下载': 'downloads', 'downloads': 'downloads',
        
        // 文档相关
        '文档': 'docs', '文档中心': 'docs', '帮助': 'docs', 
        '说明': 'docs', '指南': 'docs', 'docs': 'docs',
        
        // 新闻动态相关
        '最新动态': 'news', '动态': 'news', '新闻': 'news', 'news': 'news'
      };

      const sectionTitles = {
        home: "首页",
        features: "功能特性", 
        "ai-assistant": "AI智能助手",
        "api-key": "API密钥管理",
        downloads: "工具下载",
        docs: "文档中心",
        news: "最新动态"
      };

      // 智能匹配目标区域
      let targetSection = null;
      const normalizedTarget = target.trim();
      
      // 1. 直接匹配
      if (sectionMapping[normalizedTarget]) {
        targetSection = sectionMapping[normalizedTarget];
      }
      // 2. 包含匹配
      else {
        for (const [keyword, sectionId] of Object.entries(sectionMapping)) {
          if (normalizedTarget.includes(keyword) || keyword.includes(normalizedTarget)) {
            targetSection = sectionId;
            break;
          }
        }
      }

      // 3. 基于当前页面上下文的智能推荐
      if (!targetSection && currentPageContent) {
        const currentHeadings = currentPageContent.structure?.headings || [];
        const headingTexts = currentHeadings.map(h => h.text.toLowerCase());
        
        for (const headingText of headingTexts) {
          if (headingText.includes(normalizedTarget.toLowerCase())) {
            // 找到匹配的标题，尝试映射到section
            for (const [keyword, sectionId] of Object.entries(sectionMapping)) {
              if (headingText.includes(keyword.toLowerCase())) {
                targetSection = sectionId;
                break;
              }
            }
            break;
          }
        }
      }

      console.log(`[增强版导航] 映射结果: "${target}" -> "${targetSection}"`);

      if (!targetSection) {
        console.warn(`[增强版导航] 未找到匹配的区域: "${target}"`);
        return `❌ 未找到"${target}"对应的页面区域。\n\n可用区域：${Object.values(sectionTitles).join('、')}\n\n💡 您也可以描述您要找的内容，我会根据当前页面情况为您推荐。`;
      }

      // 查找DOM元素
      const element = document.getElementById(targetSection);
      console.log(`[增强版导航] 查找元素 ID="${targetSection}":`, element ? '找到' : '未找到');
      
      if (element) {
        // 检查元素是否已经在视口中
        const rect = element.getBoundingClientRect();
        const isInViewport = rect.top >= 0 && rect.bottom <= window.innerHeight;
        
        if (isInViewport) {
          return `✅ 您已经在${sectionTitles[targetSection]}区域了`;
        }
        
        element.scrollIntoView({ 
          behavior: 'smooth',
          block: 'start',
          inline: 'nearest'
        });
        
        // 记录导航历史
        if (window.userInteractionHistory) {
          window.userInteractionHistory.navigation = window.userInteractionHistory.navigation || [];
          window.userInteractionHistory.navigation.push({
            timestamp: new Date().toISOString(),
            target: target,
            section: targetSection,
            success: true
          });
        }
        
        console.log(`[增强版导航] 导航成功到: ${sectionTitles[targetSection]}`);
        return `✅ 已成功导航到${sectionTitles[targetSection]}区域`;
      } else {
        console.error(`[增强版导航] DOM元素不存在: ID="${targetSection}"`);
        return `❌ 页面区域"${sectionTitles[targetSection]}"暂时不可用，请稍后再试\n\n💡 您可以尝试刷新页面或使用其他导航方式。`;
      }
    },
    render: ({ status, args, result }) => {
      if (status === 'executing') {
        return (
          <div className="flex items-center gap-2 text-blue-600 bg-blue-50 p-3 rounded-lg">
            <div className="animate-spin w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full"></div>
            🧭 正在导航到 "{args.target}"...
          </div>
        );
      }
      if (status === 'complete') {
        const isSuccess = result.includes('✅');
        const isAlreadyThere = result.includes('已经在');
        
        return (
          <div className={`flex items-center gap-2 p-3 rounded-lg ${
            isSuccess 
              ? (isAlreadyThere ? 'text-green-600 bg-green-50' : 'text-blue-600 bg-blue-50')
              : 'text-orange-600 bg-orange-50'
          }`}>
            <div className="whitespace-pre-line">{result}</div>
          </div>
        );
      }
      return null;
    }
  });



  // 辅助方法：生成页面建议
  const _generatePageRecommendations = useCallback((content) => {
    const recommendations = [];
    
    // 基于页面状态生成建议
    if (content.errorInfo?.validationErrors?.length > 0) {
      recommendations.push("检测到表单验证错误，建议检查输入字段");
    }
    
    if (content.applicationState?.loading?.isLoading) {
      recommendations.push("页面正在加载中，请耐心等待");
    }
    
    if (!content.applicationState?.user?.isLoggedIn) {
      recommendations.push("您尚未登录，某些功能可能受限");
    }
    
    if (content.interactiveElements?.buttons?.length > 0) {
      recommendations.push("页面包含交互按钮，可以尝试进行相关操作");
    }
    
    if (content.formData?.forms?.length > 0) {
      recommendations.push("页面包含表单，可以填写相关信息");
    }
    
    return recommendations;
  }, []);

  // 继承其他原有Actions（登录、快速帮助等）
  useCopilotAction({
    name: "showLogin",
    description: "显示登录窗口，当用户需要访问受保护的功能时使用",
    handler: () => {
      if (user) {
        return `您已经登录了，当前用户：${user.username}`;
      }
      window.dispatchEvent(new CustomEvent('showLoginModal'));
      return "已打开登录窗口，请输入您的用户名和密码";
    },
    render: ({ status, result }) => {
      if (status === 'executing') {
        return (
          <div className="flex items-center gap-2 text-blue-600 bg-blue-50 p-2 rounded">
            🔐 正在打开登录窗口...
          </div>
        );
      }
      if (status === 'complete') {
        return (
          <div className="flex items-center gap-2 text-green-600 bg-green-50 p-2 rounded">
            ✅ {result}
          </div>
        );
      }
      return null;
    }
  });

  // 平台状态检查Action
  useCopilotAction({
    name: "checkSystemStatus",
    description: "检查平台系统状态和用户状态",
    handler: () => {
      const systemStatus = {
        platform: "运行正常 ✅",
        user: user ? `已登录: ${user.username}` : "未登录",
        services: {
          documentService: documentsLoaded ? "已加载 ✅" : "加载中 ⏳",
          pageAnalysis: pageContentLoaded ? "已启用 ✅" : "启动中 ⏳",
          contextAwareness: (documentsLoaded && pageContentLoaded) ? "已启用 ✅" : "启动中 ⏳"
        },
        capabilities: [
          "智能文档搜索",
          "实时页面分析", 
          "上下文感知导航",
          "智能内容推荐",
          "错误诊断",
          "性能监控"
        ],
        recommendation: user ? "所有功能可用，可以开始使用" : "建议先登录以获得完整功能"
      };

      return systemStatus;
    },
    render: ({ status, result }) => {
      if (status === 'executing') {
        return (
          <div className="flex items-center gap-2 text-blue-600 bg-blue-50 p-2 rounded">
            ⚙️ 正在检查系统状态...
          </div>
        );
      }
      if (status === 'complete' && result) {
        return (
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 space-y-3">
            <div className="font-medium text-gray-800">🔍 系统状态检查</div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <div className="bg-white border border-gray-200 rounded p-3">
                <div className="font-medium text-gray-700 mb-2">平台状态</div>
                <div className="text-sm text-gray-600">
                  <div>平台: {result.platform}</div>
                  <div>用户: {result.user}</div>
                </div>
              </div>
              
              <div className="bg-white border border-gray-200 rounded p-3">
                <div className="font-medium text-gray-700 mb-2">服务状态</div>
                <div className="text-sm text-gray-600">
                  <div>文档服务: {result.services.documentService}</div>
                  <div>页面分析: {result.services.pageAnalysis}</div>
                  <div>上下文感知: {result.services.contextAwareness}</div>
                </div>
              </div>
            </div>

            <div className="bg-white border border-gray-200 rounded p-3">
              <div className="font-medium text-gray-700 mb-2">可用功能</div>
              <div className="text-sm text-gray-600">
                {result.capabilities.join(' • ')}
              </div>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded p-3">
              <div className="font-medium text-blue-800 mb-1">建议</div>
              <div className="text-sm text-blue-700">{result.recommendation}</div>
            </div>
          </div>
        );
      }
      return null;
    }
  });

  // 文档加载诊断Action
  useCopilotAction({
    name: "diagnoseDocumentLoading",
    description: "诊断文档库加载问题并提供修复建议",
    parameters: [
      {
        name: "autoFix",
        type: "boolean",
        description: "是否尝试自动修复问题",
        required: false
      }
    ],
    handler: async ({ autoFix = false }) => {
      try {
        console.log('开始文档加载诊断...');
        const diagnosticResults = await documentLoadingDiagnostic.runFullDiagnostic();

        let fixResults = null;
        if (autoFix && diagnosticResults.summary.failed > 0) {
          console.log('尝试自动修复...');
          fixResults = await documentLoadingDiagnostic.attemptAutoFix();
        }

        return {
          diagnostic: diagnosticResults,
          autoFix: fixResults,
          summary: {
            totalTests: diagnosticResults.tests.length,
            passed: diagnosticResults.summary.passed,
            failed: diagnosticResults.summary.failed,
            warnings: diagnosticResults.summary.warnings,
            recommendations: diagnosticResults.recommendations
          }
        };
      } catch (error) {
        console.error('诊断失败:', error);
        return {
          error: true,
          message: "诊断过程失败",
          details: error.message
        };
      }
    },
    render: ({ status, _args, result }) => {
      if (status === 'executing') {
        return (
          <div className="flex items-center gap-2 text-blue-600 bg-blue-50 p-3 rounded-lg">
            <div className="animate-spin w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full"></div>
            🔧 正在诊断文档库状态...
          </div>
        );
      }

      if (status === 'complete' && result) {
        if (result.error) {
          return (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="font-medium text-red-800">❌ 诊断失败</div>
              <div className="text-sm text-red-700">{result.message}</div>
            </div>
          );
        }

        const { summary } = result;

        return (
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 my-2">
            <div className="font-medium text-gray-800 mb-3">🔧 文档库诊断报告</div>

            <div className="grid grid-cols-3 gap-4 mb-4 text-sm">
              <div className="text-center">
                <div className="text-green-600 font-bold text-lg">{summary.passed}</div>
                <div className="text-gray-600">通过</div>
              </div>
              <div className="text-center">
                <div className="text-yellow-600 font-bold text-lg">{summary.warnings}</div>
                <div className="text-gray-600">警告</div>
              </div>
              <div className="text-center">
                <div className="text-red-600 font-bold text-lg">{summary.failed}</div>
                <div className="text-gray-600">失败</div>
              </div>
            </div>

            {summary.recommendations.length > 0 && (
              <div className="mb-3">
                <div className="font-medium text-gray-700 mb-2">💡 修复建议:</div>
                <ul className="text-sm text-gray-600 space-y-1">
                  {summary.recommendations?.slice(0, 3).map((rec, index) => (
                    <li key={`summary-rec-${index}-${rec.substring(0, 10)}`} className="flex items-start gap-2">
                      <span className="text-blue-500">•</span>
                      <span>{rec}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        );
      }

      return null;
    }
  });

  // 始终显示页面内容，不阻塞用户体验
  return (
    <div className="enhanced-copilot-provider">
      {children}
    </div>
  );
};

// 智能问题路由辅助函数
const _analyzeQuestionType = (question) => {
  console.log('[Question Analysis] 开始分析问题:', question);
  const q = question.toLowerCase();
  console.log('[Question Analysis] 转换为小写:', q);

  // 页面内容相关的关键词
  const pageContentKeywords = [
    '页面', '当前', '这里', '显示', '展示', '界面', '屏幕',
    '工具下载', '下载', '有什么', '包含', '列出', '看到',
    '页面上', '这个页面', '当前页面', '界面上'
  ];

  // 文档搜索相关的关键词
  const documentationKeywords = [
    '如何', '怎么', '教程', '指南', '配置', '安装', '设置',
    '使用方法', '操作步骤', '帮助', '说明', '文档', '手册'
  ];

  // 混合类型关键词（需要同时使用页面分析和文档搜索）
  const hybridKeywords = [
    '详细介绍', '完整说明', '全面了解', '深入了解'
  ];

  let pageScore = 0;
  let docScore = 0;
  let hybridScore = 0;

  // 计算各类型得分
  const pageMatches = [];
  pageContentKeywords.forEach(keyword => {
    if (q.includes(keyword)) {
      pageScore += 2;
      pageMatches.push(keyword);
    }
  });

  const docMatches = [];
  documentationKeywords.forEach(keyword => {
    if (q.includes(keyword)) {
      docScore += 2;
      docMatches.push(keyword);
    }
  });

  const hybridMatches = [];
  hybridKeywords.forEach(keyword => {
    if (q.includes(keyword)) {
      hybridScore += 3;
      hybridMatches.push(keyword);
    }
  });

  console.log('[Question Analysis] 关键词匹配结果:');
  console.log('  页面内容关键词匹配:', pageMatches, '得分:', pageScore);
  console.log('  文档搜索关键词匹配:', docMatches, '得分:', docScore);
  console.log('  混合模式关键词匹配:', hybridMatches, '得分:', hybridScore);

  // 特殊规则：如果问题明确涉及当前页面内容
  const specialPageRules = [];
  if (q.includes('工具下载有什么')) {
    pageScore += 5;
    specialPageRules.push('工具下载有什么');
  }
  if (q.includes('页面上有')) {
    pageScore += 5;
    specialPageRules.push('页面上有');
  }
  if (q.includes('这里显示')) {
    pageScore += 5;
    specialPageRules.push('这里显示');
  }

  // 特殊规则：如果问题涉及操作方法
  const specialDocRules = [];
  if (q.includes('如何使用')) {
    docScore += 5;
    specialDocRules.push('如何使用');
  }
  if (q.includes('怎么安装')) {
    docScore += 5;
    specialDocRules.push('怎么安装');
  }
  if (q.includes('配置方法')) {
    docScore += 5;
    specialDocRules.push('配置方法');
  }

  console.log('[Question Analysis] 特殊规则匹配:');
  console.log('  页面内容特殊规则:', specialPageRules, '额外得分:', specialPageRules.length * 5);
  console.log('  文档搜索特殊规则:', specialDocRules, '额外得分:', specialDocRules.length * 5);

  // 决定问题类型
  const maxScore = Math.max(pageScore, docScore, hybridScore);
  console.log('[Question Analysis] 最终得分 - 页面:', pageScore, '文档:', docScore, '混合:', hybridScore, '最高:', maxScore);

  let type, confidence, reasoning, recommendedAction;

  if (hybridScore === maxScore && hybridScore > 0) {
    type = 'hybrid';
    confidence = Math.min(hybridScore / 5, 1);
    reasoning = '问题需要结合页面内容和文档信息来完整回答';
    recommendedAction = '同时使用页面分析和文档搜索';
  } else if (pageScore === maxScore && pageScore > 0) {
    type = 'page-content';
    confidence = Math.min(pageScore / 7, 1);
    reasoning = '问题主要关于当前页面显示的内容';
    recommendedAction = '使用页面分析功能';
  } else if (docScore === maxScore && docScore > 0) {
    type = 'documentation';
    confidence = Math.min(docScore / 7, 1);
    reasoning = '问题主要关于使用方法或技术文档';
    recommendedAction = '使用文档搜索功能';
  } else {
    type = 'page-content'; // 默认使用页面分析
    confidence = 0.3;
    reasoning = '无法明确判断问题类型，默认使用页面分析';
    recommendedAction = '使用页面分析功能';
  }

  const result = { type, confidence, reasoning, recommendedAction };
  console.log('[Question Analysis] 分析结果:', result);
  return result;
};

const _formatPageContentAnswer = (pageAnalysis, question) => {
  console.log('[Format Page Content] 开始格式化页面内容答案');
  console.log('[Format Page Content] 页面分析数据:', pageAnalysis);

  const downloadContent = pageAnalysis.appState?.downloadContent || {};
  console.log('[Format Page Content] 下载内容数据:', downloadContent);

  const q = question.toLowerCase();
  console.log('[Format Page Content] 问题关键词检查:', q);

  // 如果问题涉及下载内容，重点返回下载信息
  const isDownloadQuestion = q.includes('下载') || q.includes('工具') || q.includes('软件');
  console.log('[Format Page Content] 是否为下载相关问题:', isDownloadQuestion);

  if (isDownloadQuestion) {
    const result = {
      answerType: 'download-focused',
      downloadSummary: {
        hasDownloads: downloadContent.hasDownloadSection,
        totalItems: downloadContent.totalDownloads,
        categories: downloadContent.categories?.map(cat => cat.name) || [],
        tools: downloadContent.tools?.map(tool => ({
          name: tool.name,
          description: tool.description,
          version: tool.version,
          platform: tool.platform,
          downloadUrl: tool.downloadUrl
        })) || [],
        software: downloadContent.software?.map(sw => ({
          name: sw.name,
          description: sw.description,
          version: sw.version,
          platform: sw.platform
        })) || [],
        downloadButtons: downloadContent.downloadButtons?.map(btn => ({
          text: btn.text,
          disabled: btn.disabled
        })) || [],
        downloadLinks: downloadContent.downloadLinks?.map(link => ({
          text: link.text,
          fileType: link.fileType,
          href: link.href
        })) || []
      },
      pageContext: {
        title: pageAnalysis.title,
        url: pageAnalysis.url,
        mainHeadings: pageAnalysis.structure?.headings?.filter(h => h.level <= 2).map(h => h.text) || []
      },
      userGuidance: _generateDownloadGuidance(downloadContent)
    };

    console.log('[Format Page Content] 下载相关答案生成完成:', result);
    return result;
  }

  // 通用页面内容回答
  const generalResult = {
    answerType: 'general-page',
    pageOverview: {
      title: pageAnalysis.title,
      url: pageAnalysis.url,
      pageType: pageAnalysis.appState?.pageType || 'unknown',
      primaryPurpose: pageAnalysis.appState?.primaryPurpose || '未知'
    },
    contentSummary: {
      sections: pageAnalysis.structure?.sections?.length || 0,
      headings: pageAnalysis.structure?.headings?.length || 0,
      interactiveElements: Object.values(pageAnalysis.interactiveElements || {}).flat().length,
      hasDownloads: downloadContent.hasDownloadSection,
      downloadCount: downloadContent.totalDownloads
    },
    availableActions: pageAnalysis.interactiveElements?.buttons?.slice(0, 5).map(btn => ({
      text: btn.text,
      type: btn.type,
      disabled: btn.disabled
    })) || []
  };

  console.log('[Format Page Content] 通用页面答案生成完成:', generalResult);
  return generalResult;
};

const _formatDocumentAnswer = (searchResults, question) => {
  return {
    answerType: 'documentation',
    searchQuery: question,
    resultsCount: searchResults.results?.length || 0,
    documents: searchResults.results?.map(result => ({
      title: result.title,
      content: result.content,
      relevance: result.score,
      source: result.source
    })) || [],
    suggestions: searchResults.suggestions || []
  };
};

const _formatHybridAnswer = (pageAnalysis, searchResults, question) => {
  return {
    answerType: 'hybrid',
    pageContent: _formatPageContentAnswer(pageAnalysis, question),
    documentation: _formatDocumentAnswer(searchResults, question),
    combinedInsights: _generateCombinedInsights(pageAnalysis, searchResults, question)
  };
};

const _generateDownloadGuidance = (downloadContent) => {
  const guidance = [];

  if (!downloadContent.hasDownloadSection) {
    guidance.push('当前页面未检测到下载内容');
    return guidance;
  }

  if (downloadContent.tools?.length > 0) {
    guidance.push(`发现 ${downloadContent.tools.length} 个工具可供下载`);
    downloadContent.tools.slice(0, 3).forEach(tool => {
      guidance.push(`- ${tool.name}${tool.version ? ` (${tool.version})` : ''}${tool.platform ? ` - ${tool.platform}` : ''}`);
    });
  }

  if (downloadContent.software?.length > 0) {
    guidance.push(`发现 ${downloadContent.software.length} 个软件可供下载`);
  }

  if (downloadContent.downloadButtons?.length > 0) {
    const enabledButtons = downloadContent.downloadButtons.filter(btn => !btn.disabled);
    if (enabledButtons.length > 0) {
      guidance.push(`可点击的下载按钮: ${enabledButtons.map(btn => btn.text).join(', ')}`);
    }
  }

  if (downloadContent.categories?.length > 0) {
    guidance.push(`下载分类: ${downloadContent.categories.map(cat => cat.name).join(', ')}`);
  }

  return guidance;
};

const _generateCombinedInsights = (pageAnalysis, searchResults, _question) => {
  const insights = [];
  const downloadContent = pageAnalysis.appState?.downloadContent || {};

  // 结合页面内容和文档信息生成洞察
  if (downloadContent.hasDownloadSection && searchResults.results?.length > 0) {
    insights.push('页面提供了下载选项，文档中包含相关的使用指南');
  }

  if (downloadContent.tools?.length > 0) {
    const toolNames = downloadContent.tools.map(t => t.name).join(', ');
    insights.push(`页面展示的工具 (${toolNames}) 在文档中可能有详细的安装和配置说明`);
  }

  return insights;
};

const _renderSmartRouterData = (result) => {
  if (!result?.data) return null;

  if (result.data?.answerType === 'download-focused') {
    const download = result.data?.downloadSummary || {};
    return (
      <div className="space-y-3">
        <div className="text-sm">
          <span className="font-medium text-gray-700">下载内容概览:</span>
          <div className="mt-1 text-gray-600">
            {download.hasDownloads ?
              `发现 ${download.totalItems} 个下载项目` :
              '未检测到下载内容'
            }
          </div>
        </div>

        {download.tools?.length > 0 && (
          <div className="text-sm">
            <span className="font-medium text-gray-700">可用工具:</span>
            <ul className="mt-1 text-gray-600 list-disc list-inside">
              {download.tools.slice(0, 3).map((tool, index) => (
                <li key={`tool-${index}-${tool?.name || index}`}>
                  {tool?.name || '未知工具'}
                  {tool?.version && ` (${tool.version})`}
                  {tool?.platform && ` - ${tool.platform}`}
                </li>
              ))}
            </ul>
          </div>
        )}

        {download.downloadButtons?.length > 0 && (
          <div className="text-sm">
            <span className="font-medium text-gray-700">下载按钮:</span>
            <div className="mt-1 flex flex-wrap gap-1">
              {download.downloadButtons.slice(0, 5).map((btn, index) => (
                <span key={`btn-${index}-${btn.text || index}`} className={`px-2 py-1 rounded text-xs ${
                  btn.disabled ? 'bg-gray-200 text-gray-500' : 'bg-blue-100 text-blue-700'
                }`}>
                  {btn.text}
                </span>
              ))}
            </div>
          </div>
        )}

        {result.data.userGuidance?.length > 0 && (
          <div className="text-sm">
            <span className="font-medium text-gray-700">使用指导:</span>
            <ul className="mt-1 text-gray-600 list-disc list-inside">
              {result.data.userGuidance.slice(0, 3).map((guide, index) => (
                <li key={`guide-${index}-${guide.substring(0, 10)}`}>{guide}</li>
              ))}
            </ul>
          </div>
        )}
      </div>
    );
  }

  if (result.data.answerType === 'general-page') {
    const page = result.data.pageOverview;
    const content = result.data.contentSummary;
    return (
      <div className="space-y-3">
        <div className="text-sm">
          <span className="font-medium text-gray-700">页面信息:</span>
          <div className="mt-1 text-gray-600">
            {page.title} - {page.primaryPurpose}
          </div>
        </div>

        <div className="text-sm">
          <span className="font-medium text-gray-700">内容统计:</span>
          <div className="mt-1 text-gray-600">
            {content.sections} 个区域, {content.headings} 个标题, {content.interactiveElements} 个交互元素
            {content.hasDownloads && `, ${content.downloadCount} 个下载项`}
          </div>
        </div>

        {result.data.availableActions?.length > 0 && (
          <div className="text-sm">
            <span className="font-medium text-gray-700">可用操作:</span>
            <div className="mt-1 flex flex-wrap gap-1">
              {result.data.availableActions.map((action, index) => (
                <span key={`action-${index}-${action.text || index}`} className={`px-2 py-1 rounded text-xs ${
                  action.disabled ? 'bg-gray-200 text-gray-500' : 'bg-green-100 text-green-700'
                }`}>
                  {action.text}
                </span>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  }

  if (result.data.answerType === 'documentation') {
    const docs = result.data;
    return (
      <div className="space-y-3">
        <div className="text-sm">
          <span className="font-medium text-gray-700">文档搜索结果:</span>
          <div className="mt-1 text-gray-600">
            找到 {docs.resultsCount} 个相关文档
          </div>
        </div>

        {docs.documents?.slice(0, 2).map((doc, index) => (
          <div key={`doc-result-${index}-${doc.title || index}`} className="text-sm border-l-2 border-blue-200 pl-3">
            <div className="font-medium text-gray-700">{doc.title}</div>
            <div className="text-gray-600 text-xs mt-1">
              {doc.content?.substring(0, 100)}...
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (result.data.answerType === 'hybrid') {
    return (
      <div className="space-y-4">
        <div className="text-sm">
          <span className="font-medium text-gray-700">混合分析结果:</span>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="border border-blue-200 rounded p-3">
            <div className="font-medium text-blue-700 text-sm mb-2">📄 页面内容</div>
            <div className="text-sm text-gray-600">
              {result.data.pageContent?.pageOverview?.title || '页面内容分析'}
            </div>
          </div>

          <div className="border border-green-200 rounded p-3">
            <div className="font-medium text-green-700 text-sm mb-2">📚 文档信息</div>
            <div className="text-sm text-gray-600">
              找到 {result.data.documentation?.resultsCount || 0} 个相关文档
            </div>
          </div>
        </div>

        {result.data.combinedInsights?.length > 0 && (
          <div className="text-sm">
            <span className="font-medium text-gray-700">综合洞察:</span>
            <ul className="mt-1 text-gray-600 list-disc list-inside">
              {result.data.combinedInsights.map((insight, index) => (
                <li key={`insight-${index}-${insight.substring(0, 10)}`}>{insight}</li>
              ))}
            </ul>
          </div>
        )}
      </div>
    );
  }

  return null;
};

// 文档状态辅助函数
const _generateDocumentStatusActions = (detailedStatus, stats) => {
  const actions = [];

  if (detailedStatus.status === 'loading') {
    actions.push({
      type: 'wait',
      text: '等待加载完成',
      description: `正在加载文档 (${detailedStatus.progress.loaded}/${detailedStatus.progress.total})`
    });

    if (detailedStatus.canPartialSearch) {
      actions.push({
        type: 'partial-search',
        text: '使用部分文档搜索',
        description: `当前可搜索 ${detailedStatus.cacheSize} 个文档`
      });
    }
  } else if (detailedStatus.status === 'partial') {
    actions.push({
      type: 'partial-search',
      text: '使用可用文档搜索',
      description: `${detailedStatus.cacheSize} 个文档可用，部分文档加载失败`
    });

    actions.push({
      type: 'retry-failed',
      text: '重试失败的文档',
      description: `重新加载 ${detailedStatus.errorInfo.failedDocuments.length} 个失败文档`
    });
  } else if (detailedStatus.status === 'complete') {
    actions.push({
      type: 'search',
      text: '开始搜索',
      description: `搜索 ${stats.totalDocuments} 个文档中的内容`
    });

    actions.push({
      type: 'browse-categories',
      text: '浏览分类',
      description: `查看 ${stats.categoriesCount} 个文档分类`
    });
  } else if (detailedStatus.status === 'error') {
    actions.push({
      type: 'refresh',
      text: '刷新页面',
      description: '重新初始化文档库'
    });

    actions.push({
      type: 'check-network',
      text: '检查网络',
      description: '确认网络连接正常'
    });
  }

  return actions;
};

// 页面分析辅助函数
const _detectPageType = (content) => {
  const url = content.url.toLowerCase();
  const title = content.title.toLowerCase();

  if (url.includes('dashboard') || title.includes('仪表板')) return 'dashboard';
  if (url.includes('profile') || title.includes('个人资料')) return 'profile';
  if (url.includes('settings') || title.includes('设置')) return 'settings';
  if (url.includes('admin') || title.includes('管理')) return 'admin';
  if (content.formData?.forms?.length > 0) return 'form';
  if (content.structure?.headings?.some(h => h.text.includes('文档'))) return 'documentation';

  return 'general';
};

const _analyzePrimaryPurpose = (content) => {
  const functionalAreas = content.appState?.functionalAreas || [];
  const buttons = content.interactiveElements?.buttons || [];
  const forms = content.formData?.forms || [];

  if (forms.length > 0) return '数据输入和表单提交';
  if (functionalAreas.some(area => area.type === 'data-display')) return '数据展示和查看';
  if (buttons.some(btn => btn.text.includes('创建') || btn.text.includes('添加'))) return '内容创建和管理';
  if (functionalAreas.some(area => area.type === 'navigation')) return '导航和浏览';

  return '信息展示和交互';
};

const _calculateNavigationDepth = (content) => {
  const navigation = content.navigationInfo || {};
  return Math.max(
    (navigation.breadcrumbs || []).length,
    (navigation.menuItems || []).filter(item => item.hasSubmenu).length
  );
};

const _categorizeButtons = (buttons) => {
  const categories = {
    primary: [],
    secondary: [],
    action: [],
    navigation: [],
    form: []
  };

  buttons.forEach(btn => {
    const text = btn.text.toLowerCase();
    const type = btn.type || '';

    if (btn.primary || type.includes('primary')) {
      categories.primary.push(btn);
    } else if (text.includes('取消') || text.includes('关闭')) {
      categories.secondary.push(btn);
    } else if (text.includes('提交') || text.includes('保存')) {
      categories.form.push(btn);
    } else if (text.includes('查看') || text.includes('详情')) {
      categories.navigation.push(btn);
    } else {
      categories.action.push(btn);
    }
  });

  return categories;
};

const _analyzeFormsInDetail = (forms) => {
  return forms.map(form => ({
    name: form.name || '未命名表单',
    purpose: _detectFormPurpose(form),
    complexity: _calculateFormComplexity(form),
    validation: _analyzeFormValidation(form),
    accessibility: _checkFormAccessibility(form)
  }));
};

const _detectFormPurpose = (form) => {
  const action = (form.action || '').toLowerCase();
  const fields = form.fields || [];

  if (action.includes('login') || fields.some(f => f.type === 'password')) return '用户登录';
  if (action.includes('register') || fields.length > 5) return '用户注册';
  if (action.includes('search') || fields.some(f => f.type === 'search')) return '搜索查询';
  if (fields.some(f => f.type === 'email')) return '联系或通信';

  return '数据输入';
};

const _calculateFormComplexity = (form) => {
  const fieldCount = (form.fields || []).length;
  if (fieldCount <= 3) return 'simple';
  if (fieldCount <= 8) return 'medium';
  return 'complex';
};

const _analyzeFormValidation = (form) => {
  const fields = form.fields || [];
  return {
    hasRequired: fields.some(f => f.required),
    hasValidation: fields.some(f => f.pattern || f.minLength || f.maxLength),
    fieldTypes: [...new Set(fields.map(f => f.type))]
  };
};

const _checkFormAccessibility = (form) => {
  const fields = form.fields || [];
  return {
    hasLabels: fields.every(f => f.label),
    hasFieldsets: form.fieldsets > 0,
    hasErrorHandling: form.errorHandling || false
  };
};

const _analyzeLinks = (links) => {
  const internal = links.filter(link => !link.href.startsWith('http'));
  const external = links.filter(link => link.href.startsWith('http'));

  return {
    total: links.length,
    internal: internal.length,
    external: external.length,
    downloadLinks: links.filter(link => link.href.includes('download')).length,
    emailLinks: links.filter(link => link.href.startsWith('mailto:')).length
  };
};

const _generateQuickActions = (content) => {
  const actions = [];
  const buttons = content.interactiveElements?.buttons || [];
  const forms = content.formData?.forms || [];

  // 基于按钮生成快速操作
  buttons.slice(0, 5).forEach(btn => {
    if (!btn.disabled) {
      actions.push({
        type: 'button',
        text: `点击"${btn.text}"`,
        description: _getButtonActionDescription(btn)
      });
    }
  });

  // 基于表单生成快速操作
  forms.slice(0, 2).forEach(form => {
    actions.push({
      type: 'form',
      text: `填写${form.name || '表单'}`,
      description: `完成${_detectFormPurpose(form)}操作`
    });
  });

  return actions;
};

const _getButtonActionDescription = (btn) => {
  const text = btn.text.toLowerCase();
  if (text.includes('保存')) return '保存当前更改';
  if (text.includes('提交')) return '提交表单数据';
  if (text.includes('删除')) return '删除选中项目';
  if (text.includes('编辑')) return '编辑当前内容';
  if (text.includes('查看')) return '查看详细信息';
  return '执行相关操作';
};

const _generateStepByStepGuide = (content) => {
  const guide = [];
  const workflow = content.appState?.workflowState || {};

  if (workflow.currentStep) {
    guide.push(`当前步骤: ${workflow.currentStep.text}`);
  }

  if (workflow.availableActions?.length > 0) {
    guide.push('可执行操作:');
    workflow.availableActions.slice(0, 3).forEach(action => {
      guide.push(`- ${action.text}`);
    });
  }

  if (workflow.nextSteps?.length > 0) {
    guide.push('后续步骤:');
    workflow.nextSteps.forEach(step => {
      guide.push(`- ${step}`);
    });
  }

  return guide.length > 0 ? guide : ['页面已准备就绪，可以开始操作'];
};

const _generateTroubleshooting = (content) => {
  const issues = [];
  const errors = content.errorInfo?.validationErrors || [];
  const accessibility = content.appState?.accessibility || {};

  if (errors.length > 0) {
    issues.push(`发现 ${errors.length} 个验证错误，请检查表单输入`);
  }

  if (accessibility.issues?.length > 0) {
    issues.push(`页面可用性评分: ${accessibility.score}/100`);
    accessibility.issues.slice(0, 2).forEach(issue => {
      issues.push(`- ${issue}`);
    });
  }

  const disabledElements = content.interactiveElements?.buttons?.filter(btn => btn.disabled) || [];
  if (disabledElements.length > 0) {
    issues.push(`${disabledElements.length} 个按钮被禁用，可能需要满足特定条件`);
  }

  return issues.length > 0 ? issues : ['页面运行正常，未发现明显问题'];
};

const _generateUsageTips = (content) => {
  const tips = [];
  const functionalAreas = content.appState?.functionalAreas || [];
  const reactState = content.appState?.reactState || {};

  if (functionalAreas.some(area => area.type === 'forms')) {
    tips.push('💡 填写表单时，注意必填字段标记');
  }

  if (functionalAreas.some(area => area.type === 'data-display')) {
    tips.push('📊 可以使用表格排序和筛选功能');
  }

  if (reactState.hasReact) {
    tips.push('⚡ 页面支持实时更新，无需手动刷新');
  }

  if (content.interactiveElements?.buttons?.some(btn => btn.text.includes('搜索'))) {
    tips.push('🔍 使用搜索功能快速找到所需内容');
  }

  return tips.length > 0 ? tips : ['🎯 根据页面提示进行操作'];
};

const _detectLoadingStates = (content) => {
  return {
    isLoading: content.applicationState?.loading?.isLoading || false,
    loadingElements: content.applicationState?.loading?.elements || [],
    hasSpinners: document.querySelectorAll('.loading, .spinner, [data-loading]').length > 0
  };
};

const _detectErrors = (content) => {
  return {
    validationErrors: content.errorInfo?.validationErrors || [],
    systemErrors: content.errorInfo?.systemErrors || [],
    hasErrorMessages: document.querySelectorAll('.error, .alert-danger').length > 0
  };
};

const _detectNotifications = (content) => {
  return {
    count: content.applicationState?.notifications?.count || 0,
    types: content.applicationState?.notifications?.types || [],
    hasToasts: document.querySelectorAll('.toast, .notification').length > 0
  };
};

const _generateEnhancedRecommendations = (content) => {
  const recommendations = [];
  const workflow = content.appState?.workflowState || {};
  const accessibility = content.appState?.accessibility || {};

  // 工作流建议
  if (workflow.blockers?.length > 0) {
    recommendations.push({
      type: 'workflow',
      priority: 'high',
      message: `解决 ${workflow.blockers.length} 个阻塞问题以继续操作`
    });
  }

  // 可用性建议
  if (accessibility.score < 80) {
    recommendations.push({
      type: 'accessibility',
      priority: 'medium',
      message: '页面可用性有待改善，建议关注无障碍访问'
    });
  }

  // 功能建议
  if (workflow.availableActions?.length > 0) {
    recommendations.push({
      type: 'action',
      priority: 'low',
      message: `发现 ${workflow.availableActions.length} 个可用操作，可以尝试使用`
    });
  }

  return recommendations;
};

const _suggestNextSteps = (content) => {
  const steps = [];
  const workflow = content.appState?.workflowState || {};
  const forms = content.formData?.forms || [];

  if (workflow.currentStep && workflow.nextSteps?.length > 0) {
    steps.push(...workflow.nextSteps.slice(0, 3));
  } else if (forms.length > 0) {
    steps.push('填写并提交表单');
  } else if (content.interactiveElements?.buttons?.length > 0) {
    const primaryButton = content.interactiveElements.buttons.find(btn => btn.primary);
    if (primaryButton) {
      steps.push(`点击"${primaryButton.text}"继续`);
    }
  }

  if (steps.length === 0) {
    steps.push('浏览页面内容，了解可用功能');
  }

  return steps;
};

const _generateFallbackAnalysis = () => {
  return {
    pageInfo: {
      url: window.location.href,
      title: document.title,
      timestamp: new Date().toISOString(),
      pageType: 'unknown',
      primaryPurpose: '页面分析失败，无法确定主要用途'
    },
    contentAnalysis: {
      structure: {
        sectionsCount: document.querySelectorAll('section, div[id]').length,
        headingsCount: document.querySelectorAll('h1, h2, h3, h4, h5, h6').length,
        mainHeadings: Array.from(document.querySelectorAll('h1, h2')).map(h => h.textContent?.trim() || ''),
        navigationDepth: 0
      },
      interactivity: {
        totalInteractiveElements: document.querySelectorAll('button, a, input').length,
        buttonsByType: { primary: [], secondary: [], action: [], navigation: [], form: [] },
        formsAnalysis: [],
        linksAnalysis: { total: 0, internal: 0, external: 0 }
      },
      functionality: {
        functionalAreas: [],
        workflowState: {},
        reactComponents: 0,
        stateManagement: []
      },
      accessibility: { score: 0, issues: ['无法进行可用性分析'], recommendations: [] }
    },
    userGuidance: {
      quickActions: [],
      stepByStepGuide: ['页面分析失败，请手动探索页面功能'],
      troubleshooting: ['建议刷新页面或检查网络连接'],
      tips: ['如果问题持续，请联系技术支持']
    },
    currentState: {
      user: { loggedIn: false },
      workflow: {},
      loading: { isLoading: false, loadingElements: [], hasSpinners: false },
      errors: { validationErrors: [], systemErrors: [], hasErrorMessages: false },
      notifications: { count: 0, types: [], hasToasts: false }
    },
    recommendations: [{
      type: 'system',
      priority: 'high',
      message: '页面分析功能异常，建议刷新页面重试'
    }],
    nextSteps: ['刷新页面', '检查网络连接', '联系技术支持']
  };
};



export default EnhancedCopilotProvider;