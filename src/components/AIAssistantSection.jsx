import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { FaCode, FaRobot, FaBolt, FaShieldAlt, FaPlay, FaComments, FaCog, FaLightbulb, FaSpinner } from 'react-icons/fa';
import metricsService from '../services/metricsService';


const AIAssistantSection = () => {
  const [activeDemo, setActiveDemo] = useState(0);
  const [supportedModels, setSupportedModels] = useState([]);
  const [modelsLoading, setModelsLoading] = useState(true);
  const [isRealTimeData, setIsRealTimeData] = useState(false);
  const [demoLoading, setDemoLoading] = useState(false);
  const [demoResponses, setDemoResponses] = useState({});

  const features = useMemo(() => [
    {
      icon: <FaCode />,
      title: '智能代码生成',
      description: '描述需求，AI自动生成完整、可运行的代码',
      example: '生成Java类、C语言函数、数据结构实现等',
      benefit: '提高开发效率 5倍'
    },
    {
      icon: <FaComments />,
      title: '24/7 技术问答',
      description: '随时解答编程疑问，提供最佳实践建议',
      example: '调试错误、性能优化、算法设计咨询',
      benefit: '即时获得专家级指导'
    },
    {
      icon: <FaBolt />,
      title: '实时代码分析',
      description: '智能检测代码问题，提供优化建议',
      example: '发现内存泄漏、性能瓶颈、代码坏味道',
      benefit: '提升代码质量 90%'
    },
    {
      icon: <FaShieldAlt />,
      title: '企业级安全',
      description: '代码安全审查，确保符合企业标准',
      example: '缓冲区溢出检测、SQL注入防护、安全编码规范',
      benefit: '零安全风险'
    }
  ], []);

  // 多场景演示示例，涵盖代码生成、审核、解释、优化等 - Use useMemo to prevent recreation
  const demos = useMemo(() => [
    {
      title: "Java代码生成",
      userQuery: "帮我写一个Java线程安全的单例模式实现，要求支持懒加载",
      category: "代码生成",
      isRealTime: true
    },
    {
      title: "C代码安全审核",
      userQuery: "请审核这段C代码的安全性，找出潜在的漏洞：\n\n```c\nvoid copy_data(char *dest, char *src) {\n    while(*src) {\n        *dest++ = *src++;\n    }\n    *dest = '\\0';\n}\n```",
      category: "代码审核",
      isRealTime: true
    },
    {
      title: "Java概念解释",
      userQuery: "请详细解释Java中的垃圾回收机制，包括不同的GC算法和适用场景",
      category: "概念解释",
      isRealTime: true
    },
    {
      title: "C性能优化",
      userQuery: "这段C代码性能较差，请分析瓶颈并提供优化方案：\n\n```c\nint find_max(int arr[], int n) {\n    int max = arr[0];\n    for(int i = 0; i < n; i++) {\n        for(int j = 0; j < n; j++) {\n            if(arr[j] > max) {\n                max = arr[j];\n            }\n        }\n    }\n    return max;\n}\n```",
      category: "性能优化",
      isRealTime: true
    },
    {
      title: "Java错误诊断",
      userQuery: "这段Java代码运行时出现NullPointerException，请帮我分析原因并修复：\n\n```java\npublic class UserService {\n    private Map<String, User> users;\n    \n    public User getUser(String id) {\n        return users.get(id);\n    }\n    \n    public void addUser(User user) {\n        users.put(user.getId(), user);\n    }\n}\n```",
      category: "错误诊断",
      isRealTime: true
    },
    {
      title: "C算法解释",
      userQuery: "请解释这个C语言二分查找算法的工作原理，包括时间复杂度分析：\n\n```c\nint binary_search(int arr[], int n, int target) {\n    int left = 0, right = n - 1;\n    while(left <= right) {\n        int mid = left + (right - left) / 2;\n        if(arr[mid] == target) return mid;\n        if(arr[mid] < target) left = mid + 1;\n        else right = mid - 1;\n    }\n    return -1;\n}\n```",
      category: "算法解释",
      isRealTime: true
    }
  ], []);



  // 获取演示回复 - Use useCallback to prevent recreation on every render
  const getDemoResponse = useCallback(async (demoIndex) => {
    const demo = demos[demoIndex];
    
    // 如果已经有缓存的回复，直接返回
    if (demoResponses[demoIndex]) {
      return demoResponses[demoIndex];
    }

    if (!demo.isRealTime) {
      // 返回静态回复（如果有的话）
      return demo.aiResponse || '演示回复加载中...';
    }

    setDemoLoading(true);
    try {
      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // 返回降级回复
      const fallbackResponses = {
        0: `// Java线程安全的单例模式实现（支持懒加载）

/**
 * 使用双重检查锁定（Double-Checked Locking）实现线程安全的懒加载单例
 */
public class ThreadSafeSingleton {
    // 使用volatile确保多线程环境下的可见性
    private static volatile ThreadSafeSingleton instance;
    
    // 私有构造函数，防止外部实例化
    private ThreadSafeSingleton() {
        // 防止反射攻击
        if (instance != null) {
            throw new RuntimeException("请使用getInstance()方法获取实例");
        }
    }
    
    /**
     * 获取单例实例（线程安全 + 懒加载）
     * @return 单例实例
     */
    public static ThreadSafeSingleton getInstance() {
        // 第一次检查，避免不必要的同步
        if (instance == null) {
            synchronized (ThreadSafeSingleton.class) {
                // 第二次检查，确保线程安全
                if (instance == null) {
                    instance = new ThreadSafeSingleton();
                }
            }
        }
        return instance;
    }
    
    // 业务方法示例
    public void doSomething() {
        System.out.println("执行业务逻辑...");
    }
}

// 🔧 **替代方案：枚举单例（推荐）**
public enum EnumSingleton {
    INSTANCE;
    
    public void doSomething() {
        System.out.println("枚举单例 - 天然线程安全且防反射");
    }
}

// 💡 **使用示例**
// ThreadSafeSingleton singleton = ThreadSafeSingleton.getInstance();
// EnumSingleton.INSTANCE.doSomething();`,
        1: `🔍 **C代码安全审核报告**

### ❌ **发现的严重安全漏洞：**

1. **缓冲区溢出漏洞** - 🚨 **高危**
   - 函数没有检查目标缓冲区大小
   - 可能导致栈溢出攻击
   - 攻击者可以覆盖返回地址

2. **无边界检查** - ⚠️ **中危**
   - 没有验证源字符串长度
   - 可能读取未初始化内存
   - 导致程序崩溃或信息泄露

3. **缺少输入验证** - 💡 **低危**
   - 未检查指针是否为NULL
   - 可能导致段错误

### ✅ **安全修复版本：**

\`\`\`c
#include <stdio.h>
#include <string.h>
#include <stddef.h>

/**
 * 安全的字符串复制函数
 * @param dest 目标缓冲区
 * @param dest_size 目标缓冲区大小
 * @param src 源字符串
 * @return 成功返回0，失败返回-1
 */
int safe_copy_data(char *dest, size_t dest_size, const char *src) {
    // 输入验证
    if (dest == NULL || src == NULL || dest_size == 0) {
        return -1;
    }
    
    // 使用安全的字符串复制函数
    size_t src_len = strlen(src);
    if (src_len >= dest_size) {
        // 源字符串太长，截断并确保null终止
        strncpy(dest, src, dest_size - 1);
        dest[dest_size - 1] = '\\0';
        return -1; // 表示发生了截断
    }
    
    // 安全复制
    strcpy(dest, src);
    return 0;
}

// 使用示例
int main() {
    char buffer[20];
    const char *test1 = "Hello";           // 正常情况
    const char *test2 = "This is a very long string"; // 溢出测试
    
    if (safe_copy_data(buffer, sizeof(buffer), test1) == 0) {
        printf("复制成功: %s\\n", buffer);
    }
    
    if (safe_copy_data(buffer, sizeof(buffer), test2) != 0) {
        printf("字符串被截断: %s\\n", buffer);
    }
    
    return 0;
}
\`\`\`

### 🛡️ **安全编程建议：**

1. **总是进行边界检查**
2. **使用安全的字符串函数** (strncpy, snprintf等)
3. **验证所有输入参数**
4. **考虑使用现代C库** (如safer C library)`,
        2: `📚 **Java垃圾回收机制详解**

## 🔄 **垃圾回收基本概念**

垃圾回收（GC）是Java自动内存管理的核心机制，负责回收不再使用的对象所占用的内存空间。

### 🏗️ **内存区域划分**

**年轻代（Young Generation）:**
- **Eden区**: 新对象分配的区域
- **Survivor区**: S0和S1，存放经过一次GC存活的对象
- **特点**: 大部分对象生命周期短，GC频率高

**老年代（Old Generation）:**
- 存放长期存活的对象
- 从年轻代晋升的对象
- GC频率低，但耗时长

**元空间（Metaspace，JDK 8+）:**
- 存储类的元数据信息
- 替代了永久代（PermGen）

## 🚀 **主要GC算法**

### 1. **Serial GC** - 串行收集器
- **适用场景**: 单核CPU，小型应用
- **特点**: 单线程，STW时间较长
- **参数**: \`-XX:+UseSerialGC\`

### 2. **Parallel GC** - 并行收集器
- **适用场景**: 多核CPU，吞吐量优先
- **特点**: 多线程并行，适合批处理
- **参数**: \`-XX:+UseParallelGC\`

### 3. **G1 GC** - 低延迟收集器
- **适用场景**: 大内存应用，延迟敏感
- **特点**: 分区收集，可预测停顿时间
- **参数**: \`-XX:+UseG1GC -XX:MaxGCPauseMillis=200\`

### 4. **ZGC/Shenandoah** - 超低延迟
- **适用场景**: 超大内存，极低延迟要求
- **特点**: 并发收集，停顿时间<10ms
- **参数**: \`-XX:+UseZGC\` 或 \`-XX:+UseShenandoahGC\`

## 🎯 **选择指南**

| 应用类型 | 推荐GC | 原因 |
|---------|--------|------|
| 小型应用 | Serial GC | 简单高效 |
| 批处理系统 | Parallel GC | 高吞吐量 |
| Web应用 | G1 GC | 平衡吞吐量和延迟 |
| 实时系统 | ZGC/Shenandoah | 超低延迟 |

## 🔧 **调优建议**

1. **监控GC日志**: \`-Xloggc:gc.log -XX:+PrintGCDetails\`
2. **合理设置堆大小**: \`-Xms4g -Xmx4g\`
3. **调整年轻代比例**: \`-XX:NewRatio=2\`
4. **设置停顿时间目标**: \`-XX:MaxGCPauseMillis=100\``,
        3: `🚀 **C代码性能优化分析**

### ❌ **性能问题诊断:**

**原始代码存在严重的性能问题：**

1. **时间复杂度过高** - 🚨 **严重**
   - 当前复杂度: O(n²) 
   - 嵌套循环导致不必要的重复计算
   - 对于大数组性能极差

2. **重复比较** - ⚠️ **中等**
   - 内层循环每次都从头开始
   - 大量无效的比较操作
   - CPU缓存利用率低

3. **算法设计缺陷** - 💡 **轻微**
   - 逻辑上只需要一次遍历
   - 没有利用已知信息

### ✅ **优化后的高效版本:**

\`\`\`c
#include <stdio.h>
#include <limits.h>

/**
 * 优化版本1：线性时间复杂度 O(n)
 */
int find_max_optimized(int arr[], int n) {
    if (n <= 0) return INT_MIN; // 边界检查
    
    int max = arr[0];
    for (int i = 1; i < n; i++) {  // 只需一次遍历
        if (arr[i] > max) {
            max = arr[i];
        }
    }
    return max;
}

/**
 * 优化版本2：SIMD向量化优化（现代CPU）
 */
int find_max_simd(int arr[], int n) {
    if (n <= 0) return INT_MIN;
    
    int max = arr[0];
    
    // 向量化处理（4个元素一组）
    int i;
    for (i = 1; i <= n - 4; i += 4) {
        int local_max = arr[i];
        if (arr[i+1] > local_max) local_max = arr[i+1];
        if (arr[i+2] > local_max) local_max = arr[i+2];
        if (arr[i+3] > local_max) local_max = arr[i+3];
        if (local_max > max) max = local_max;
    }
    
    // 处理剩余元素
    for (; i < n; i++) {
        if (arr[i] > max) max = arr[i];
    }
    
    return max;
}

/**
 * 优化版本3：分治法（适合并行处理）
 */
int find_max_divide_conquer(int arr[], int left, int right) {
    if (left == right) return arr[left];
    
    int mid = left + (right - left) / 2;
    int left_max = find_max_divide_conquer(arr, left, mid);
    int right_max = find_max_divide_conquer(arr, mid + 1, right);
    
    return (left_max > right_max) ? left_max : right_max;
}
\`\`\`

### 📊 **性能对比:**

| 算法版本 | 时间复杂度 | 空间复杂度 | 1万元素耗时 |
|---------|-----------|-----------|------------|
| 原始版本 | O(n²) | O(1) | ~100ms |
| 线性优化 | O(n) | O(1) | ~0.1ms |
| SIMD优化 | O(n) | O(1) | ~0.05ms |
| 分治法 | O(n) | O(log n) | ~0.08ms |

### 🎯 **优化建议:**

1. **算法层面**: 选择正确的算法复杂度
2. **编译器优化**: 使用 \`-O2\` 或 \`-O3\` 编译选项
3. **内存访问**: 提高缓存命中率
        4. **并行化**: 利用多核CPU优势`,
        4: `🔍 **Java错误诊断与修复**

### ❌ **NullPointerException 根因分析:**

**问题定位：**
1. **未初始化的Map** - 🚨 **主要原因**
   - \`private Map<String, User> users;\` 声明但未初始化
   - 调用 \`users.get(id)\` 时 users 为 null
   - 导致 NullPointerException

2. **缺少防御性编程** - ⚠️ **次要问题**
   - 没有空值检查
   - 缺少异常处理机制

### ✅ **修复方案:**

\`\`\`java
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Optional;

public class UserService {
    // 修复1：正确初始化Map，使用线程安全的实现
    private final Map<String, User> users = new ConcurrentHashMap<>();
    
    /**
     * 获取用户（安全版本）
     * @param id 用户ID
     * @return Optional包装的用户对象
     */
    public Optional<User> getUser(String id) {
        // 修复2：添加参数验证
        if (id == null || id.trim().isEmpty()) {
            return Optional.empty();
        }
        
        // 修复3：使用Optional避免null返回
        return Optional.ofNullable(users.get(id));
    }
    
    /**
     * 添加用户（安全版本）
     * @param user 用户对象
     * @return 是否添加成功
     */
    public boolean addUser(User user) {
        // 修复4：完整的输入验证
        if (user == null || user.getId() == null) {
            throw new IllegalArgumentException("用户或用户ID不能为空");
        }
        
        // 修复5：检查重复用户
        if (users.containsKey(user.getId())) {
            return false; // 用户已存在
        }
        
        users.put(user.getId(), user);
        return true;
    }
    
    /**
     * 获取用户总数
     */
    public int getUserCount() {
        return users.size();
    }
    
    /**
     * 检查用户是否存在
     */
    public boolean userExists(String id) {
        return id != null && users.containsKey(id);
    }
}

// 使用示例
class UserServiceExample {
    public static void main(String[] args) {
        UserService service = new UserService();
        
        // 安全的用户添加
        User user = new User("123", "张三");
        if (service.addUser(user)) {
            System.out.println("用户添加成功");
        }
        
        // 安全的用户查询
        Optional<User> foundUser = service.getUser("123");
        if (foundUser.isPresent()) {
            System.out.println("找到用户: " + foundUser.get().getName());
        } else {
            System.out.println("用户不存在");
        }
    }
}
\`\`\`

### 🛡️ **防御性编程最佳实践:**

1. **总是初始化集合类型字段**
2. **使用Optional处理可能为null的返回值**
3. **添加参数验证和边界检查**
4. **选择合适的集合实现（线程安全性考虑）**
5. **提供清晰的异常信息**

### 📊 **修复效果:**
- **原始代码**: 运行时崩溃 💥
- **修复后**: 安全稳定运行 ✅`,
        5: `📖 **C语言二分查找算法详解**

## 🔍 **算法原理分析**

二分查找（Binary Search）是一种高效的搜索算法，适用于**已排序**的数组。

### 🏗️ **核心思想:**
1. **分治策略**: 每次将搜索范围缩小一半
2. **比较中点**: 与目标值比较，决定搜索方向
3. **递归缩小**: 重复过程直到找到或确定不存在

### 📊 **算法步骤详解:**

\`\`\`c
int binary_search(int arr[], int n, int target) {
    int left = 0, right = n - 1;
    
    while(left <= right) {
        // 防止整数溢出的中点计算
        int mid = left + (right - left) / 2;
        
        if(arr[mid] == target) 
            return mid;        // 找到目标，返回索引
        
        if(arr[mid] < target) 
            left = mid + 1;    // 目标在右半部分
        else 
            right = mid - 1;   // 目标在左半部分
    }
    
    return -1;  // 未找到
}
\`\`\`

## ⏱️ **时间复杂度分析**

### **最佳情况**: O(1)
- 第一次比较就找到目标元素

### **最坏情况**: O(log n)
- 需要将数组分割到只剩一个元素
- 每次分割都减少一半元素

### **平均情况**: O(log n)
- 期望比较次数约为 log₂(n)

## 📈 **性能对比**

| 数组大小 | 线性搜索 | 二分搜索 | 性能提升 |
|---------|---------|---------|---------|
| 1,000 | 500次 | 10次 | 50倍 |
| 1,000,000 | 500,000次 | 20次 | 25,000倍 |
| 1,000,000,000 | 500,000,000次 | 30次 | 16,666,667倍 |

## 🔧 **算法变种与优化**

### 1. **递归版本:**
\`\`\`c
int binary_search_recursive(int arr[], int left, int right, int target) {
    if (left > right) return -1;
    
    int mid = left + (right - left) / 2;
    
    if (arr[mid] == target) return mid;
    if (arr[mid] > target) 
        return binary_search_recursive(arr, left, mid - 1, target);
    else 
        return binary_search_recursive(arr, mid + 1, right, target);
}
\`\`\`

### 2. **查找插入位置:**
\`\`\`c
int binary_search_insert_pos(int arr[], int n, int target) {
    int left = 0, right = n;
    while (left < right) {
        int mid = left + (right - left) / 2;
        if (arr[mid] < target) 
            left = mid + 1;
        else 
            right = mid;
    }
    return left;  // 返回应该插入的位置
}
\`\`\`

## 🎯 **使用场景与限制**

### ✅ **适用场景:**
- 已排序的数组或列表
- 频繁的查找操作
- 内存充足的环境

### ❌ **不适用场景:**
- 未排序的数据
- 频繁插入/删除操作
- 极小的数据集（线性搜索更简单）

## 💡 **实际应用:**
- 数据库索引查找
- 字典/词典搜索
- 游戏中的碰撞检测
- 数值计算中的根查找`
      };
      
      const fallback = fallbackResponses[demoIndex] || '演示暂时不可用，请稍后再试。';
      
      setDemoResponses(prev => ({
        ...prev,
        [demoIndex]: fallback
      }));
      
      return fallback;
    } finally {
      setDemoLoading(false);
    }
  }, [demos, demoResponses]);

  // 手动触发演示加载 - 不再自动加载，节省资源
  const handleDemoRequest = useCallback((demoIndex) => {
    if (demos[demoIndex]?.isRealTime) {
      getDemoResponse(demoIndex);
    }
  }, [demos, getDemoResponse]);

  // 模型颜色配置 - Use useMemo to prevent recreation on every render
  const modelColors = useMemo(() => [
    "text-green-400", "text-blue-400", "text-purple-400", "text-cyan-400",
    "text-pink-400", "text-yellow-400", "text-red-400", "text-indigo-400"
  ], []);

  // 根据模型数量动态计算网格布局
  const getGridLayout = useCallback((count) => {
    switch (count) {
      case 1:
        return 'grid-cols-1 justify-items-center max-w-sm mx-auto';
      case 2:
        return 'grid-cols-1 sm:grid-cols-2 max-w-2xl mx-auto';
      case 3:
        return 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 max-w-4xl mx-auto';
      case 4:
        return 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 max-w-5xl mx-auto';
      case 5:
      case 6:
        return 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 max-w-5xl mx-auto';
      case 7:
      case 8:
        return 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 max-w-6xl mx-auto';
      default:
        return 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 max-w-7xl mx-auto';
    }
  }, []);

  // 默认模型数据（当API不可用时显示） - Use useMemo to prevent recreation
  const defaultModels = useMemo(() => [
    { name: "qwen3-235b-a22b", description: "OpenAI 最强模型", color: "text-green-400" },
    { name: "Claude-3", description: "Anthropic 旗舰模型", color: "text-blue-400" },
    { name: "文心一言", description: "百度国产模型", color: "text-purple-400" },
    { name: "通义千问", description: "阿里云AI模型", color: "text-cyan-400" }
  ], []);

  // 获取AI模型数据 - Fixed dependencies to prevent infinite loop
  useEffect(() => {
    const fetchModels = async () => {
      try {
        setModelsLoading(true);
        const response = await metricsService.getModels();
        
        if (response.success) {
          if (response.data && response.data.length > 0) {
            // 处理真实模型数据
            const processedModels = response.data.map((model, index) => {
              const modelName = model.id || model.model || model.name || `模型 ${index + 1}`;
              let description = '高性能AI模型';
              
              // 为已知模型提供中文描述
              if (modelName.includes('deepseek-chat')) {
                description = 'DeepSeek对话模型 - 专业代码生成;默认上下文长度为32k(32768);';
              } else if (modelName.includes('deepseek-r1')) {
                description = 'DeepSeek推理模型 - 强化推理能力;默认上下文长度为32k(32768);';
              } else if (modelName.includes('qwen') || modelName.includes('通义')) {
                description = '通义千问 - 阿里云大语言模型;默认上下文长度为32k(32768);';
              } else if (modelName.includes('gpt')) {
                description = 'OpenAI GPT模型 - 通用对话;默认上下文长度为16k(32768);';
              } else if (modelName.includes('claude')) {
                description = 'Anthropic Claude - 安全可靠;默认上下文长度为100k(32768);';
              } else if (modelName.includes('文心') || modelName.includes('ernie')) {
                description = '文心一言 - 百度智能对话';
              }
              
              return {
                name: modelName,
                description: description,
                color: modelColors[index % modelColors.length]
              };
            });
            setSupportedModels(processedModels);
            setIsRealTimeData(true);
          } else {
            // API成功但无数据，仍然标记为真实数据来源，但显示提示
            setSupportedModels([{
              name: "暂无可用模型",
              description: "API连接正常，但当前无可用模型",
              color: "text-gray-400"
            }]);
            setIsRealTimeData(true);
          }
        } else {
          // API失败，使用默认数据
          setSupportedModels(defaultModels);
          setIsRealTimeData(false);
        }
      } catch (error) {
        console.error('❌ 获取模型数据失败:', error);
        setSupportedModels(defaultModels);
        setIsRealTimeData(false);
      } finally {
        setModelsLoading(false);
      }
    };

    fetchModels();
  }, []); // Remove unstable dependencies that were causing infinite loops

  return (
    <section id="ai-assistant" className="py-20 bg-gradient-to-b from-black to-gray-900 relative overflow-hidden">
      {/* 背景装饰 */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-96 h-96 bg-cyan-500/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* 标题区域 */}
        <div
          className="text-center mb-16"
        >
          <h2 className="text-5xl md:text-6xl font-bold mb-6">
            <span className="bg-gradient-to-r from-cyan-400 to-blue-500 bg-clip-text text-transparent">
              🤖 AI模型展示
            </span>
          </h2>
          <p className="text-xl text-gray-400 max-w-3xl mx-auto leading-relaxed">
            支持多种先进AI模型，智能代码生成与技术咨询
          </p>
        </div>

        {/* 核心功能展示 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          {features.map((feature, index) => (
            <div
              key={index}
              className="bg-gray-800/50 backdrop-blur-sm border border-gray-700 rounded-xl p-6 hover:border-cyan-500/50 transition-all duration-300"
            >
              <div className="text-cyan-400 text-3xl mb-4">{feature.icon}</div>
              <h3 className="text-xl font-semibold text-white mb-3">{feature.title}</h3>
              <p className="text-gray-300 mb-3 text-sm leading-relaxed">{feature.description}</p>
              <div className="mb-3">
                <p className="text-xs text-gray-500 mb-1">应用场景:</p>
                <p className="text-xs text-gray-400">{feature.example}</p>
              </div>
              <div className="text-xs text-cyan-400 font-semibold">
                ⚡ {feature.benefit}
              </div>
            </div>
          ))}
        </div>

        {/* AI模型支持 */}
        <div
          className="text-center mb-16"
        >
          <div className="mb-8">
            <h3 className="text-3xl font-bold text-white mb-4">
              支持多种AI模型
            </h3>
            <div className="flex items-center justify-center gap-2 text-sm">
              {modelsLoading ? (
                <div className="flex items-center gap-2 text-blue-400">
                  <FaSpinner className="animate-spin" />
                  <span>加载模型数据中...</span>
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <div className={`w-2 h-2 rounded-full ${isRealTimeData ? 'bg-green-400' : 'bg-yellow-400'}`}></div>
                  <span className={isRealTimeData ? 'text-green-400' : 'text-yellow-400'}>
                    {isRealTimeData ? '实时平台数据' : '示例模型数据'}
                  </span>
                </div>
              )}
            </div>
          </div>
          
          {modelsLoading ? (
            <div className="flex justify-center items-center py-12">
              <FaSpinner className="animate-spin text-4xl text-blue-400" />
            </div>
          ) : (
            <div className={`grid gap-6 max-w-6xl mx-auto ${getGridLayout(supportedModels.length)}`}>
              {supportedModels.map((model, index) => (
                                  <div
                    key={index}
                    className={`bg-gray-800/30 border border-gray-700 rounded-lg p-6 hover:border-cyan-500/30 transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-cyan-500/10 ${isRealTimeData ? 'border-green-500/20 hover:border-green-500/40' : ''}`}
                  >
                                      <div className="flex items-center gap-2 mb-3">
                      <div className="w-8 h-8 bg-gradient-to-br from-gray-700 to-gray-800 rounded-lg flex items-center justify-center">
                        <span className="text-lg">🤖</span>
                      </div>
                      <h4 className={`font-bold ${model.color} text-lg`}>{model.name}</h4>
                    </div>
                    <p className="text-gray-400 text-sm leading-relaxed mb-3">{model.description}</p>
                    {isRealTimeData ? (
                      <div className="flex items-center gap-1 text-xs text-green-400">
                        <div className="w-1.5 h-1.5 bg-green-400 rounded-full animate-pulse"></div>
                        <span>平台实时</span>
                      </div>
                    ) : null}
                </div>
              ))}
            </div>
          )}
        </div>

        {/* 互动演示区域 */}
        <div
          className="mb-16"
        >
          <h3 className="text-3xl font-bold text-white text-center mb-4">
            🎯 实时演示
          </h3>
          <p className="text-center text-gray-400 mb-8 max-w-3xl mx-auto">
            基于真实AI模型的多场景演示，涵盖代码生成、安全审核、概念解释、性能优化、错误诊断等。
            专注于Java和C语言，展示AI如何处理实际开发中的各种问题。
          </p>
          
          {/* 演示选项卡 */}
          <div className="flex flex-wrap justify-center gap-4 mb-8">
            {demos.map((demo, index) => (
              <button
                key={index}
                onClick={() => setActiveDemo(index)}
                className={`px-6 py-3 rounded-full font-semibold transition-all duration-300 relative ${
                  activeDemo === index
                    ? 'bg-gradient-to-r from-cyan-400 to-blue-500 text-black'
                    : 'bg-gray-800 text-gray-300 hover:bg-gray-700 border border-gray-600 hover:border-cyan-500'
                }`}
              >
                <div className="flex flex-col items-center">
                  <span className="text-sm">{demo.title}</span>
                  <span className={`text-xs mt-1 ${
                    activeDemo === index ? 'text-gray-700' : 'text-gray-500'
                  }`}>
                    {demo.category}
                  </span>
                </div>
                {demo.isRealTime ? (
                  <span className="absolute -top-1 -right-1 text-xs">🤖</span>
                ) : null}
              </button>
            ))}
          </div>

          {/* 演示内容 */}
          <div className="bg-gray-900/80 backdrop-blur-sm border border-gray-700 rounded-xl p-8 max-w-5xl mx-auto">
            <div className="flex items-center justify-between mb-6">
              <h4 className="text-lg font-semibold text-white flex items-center gap-2">
                <FaPlay className="text-cyan-400" />
                {demos[activeDemo].title}
                {demos[activeDemo]?.isRealTime ? (
                  <span className="text-xs bg-green-500/20 text-green-400 px-2 py-1 rounded-full">
                    实时生成
                  </span>
                ) : null}
              </h4>
              <div className="flex items-center gap-4">
                {demos[activeDemo]?.isRealTime ? (
                  <button
                    onClick={() => {
                      // 清除缓存，重新生成
                      setDemoResponses(prev => {
                        const updated = { ...prev };
                        delete updated[activeDemo];
                        return updated;
                      });
                      handleDemoRequest(activeDemo);
                    }}
                    disabled={demoLoading}
                    className="px-3 py-1 text-xs bg-gray-700 hover:bg-gray-600 text-gray-300 hover:text-white rounded-full transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-1"
                  >
                    {demoLoading ? (
                      <FaSpinner className="animate-spin" />
                    ) : (
                      <i className="fas fa-sync-alt"></i>
                    )}
                    刷新
                  </button>
                ) : null}
                <div className="flex gap-2">
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                </div>
              </div>
            </div>
            
            <div className="space-y-6">
              {/* 用户输入 */}
              <div className="bg-gray-800 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <div className="w-6 h-6 bg-gradient-to-r from-green-400 to-blue-500 rounded-full flex items-center justify-center text-xs text-black font-bold">
                    U
                  </div>
                  <span className="text-gray-400 text-sm">用户提问</span>
                </div>
                <p className="text-white pl-8">{demos[activeDemo].userQuery}</p>
              </div>
              
              {/* AI回复 */}
              <div className="bg-gradient-to-r from-cyan-500/10 to-blue-500/10 border border-cyan-500/20 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <div className="w-6 h-6 bg-gradient-to-r from-cyan-400 to-blue-500 rounded-full flex items-center justify-center text-xs text-black font-bold">
                    AI
                  </div>
                  <span className="text-gray-400 text-sm">智能回复</span>
                  <div className="ml-auto flex items-center gap-1 text-xs">
                    {demoLoading ? (
                      <>
                        <FaSpinner className="animate-spin text-blue-400" />
                        <span className="text-blue-400">生成中...</span>
                      </>
                    ) : demos[activeDemo]?.isRealTime ? (
                      <>
                        <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                        <span className="text-green-400">实时响应</span>
                      </>
                    ) : (
                      <>
                        <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                        <span className="text-yellow-400">示例数据</span>
                      </>
                    )}
                  </div>
                </div>
                {demoLoading ? (
                  <div className="flex items-center justify-center py-8">
                    <div className="flex items-center gap-3">
                      <FaSpinner className="animate-spin text-cyan-400 text-xl" />
                      <span className="text-gray-400">AI正在生成回复...</span>
                    </div>
                  </div>
                ) : (
                  <div className="text-gray-300 text-sm pl-8 whitespace-pre-wrap overflow-x-auto">
                    {demoResponses[activeDemo] || (
                      <div className="text-center py-8">
                        <div className="text-gray-500 mb-4"><i className="fas fa-lightbulb text-yellow-400 mr-2"></i>点击下方按钮获取AI演示内容</div>
                        <button
                          onClick={() => handleDemoRequest(activeDemo)}
                          className="px-4 py-2 bg-gradient-to-r from-cyan-400 to-blue-500 text-black rounded-lg hover:from-cyan-500 hover:to-blue-600 transition-all duration-300 font-semibold"
                        >
                          获取AI演示
                        </button>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* 行动号召 */}
        <div
          className="text-center"
        >
          <div className="bg-gradient-to-r from-cyan-500/10 to-blue-500/10 border border-cyan-500/20 rounded-2xl p-8 max-w-4xl mx-auto">
            <h3 className="text-3xl font-bold text-white mb-4">
              准备好与AI协作了吗？
            </h3>
            <p className="text-lg text-gray-400 mb-8">
              选择合适的AI模型，让智能助手成为您的编程伙伴
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                onClick={() => window.location.href = '#api-key'}
                className="px-8 py-4 bg-gradient-to-r from-cyan-400 to-blue-500 text-black font-semibold rounded-full text-lg shadow-lg shadow-cyan-500/25 flex items-center justify-center gap-2"
              >
                <FaCog />
                配置API密钥
              </button>
              <button
                onClick={() => window.location.href = '#api-key'}
                className="px-8 py-4 bg-gray-800 hover:bg-gray-700 text-white border border-gray-600 hover:border-cyan-500 font-semibold rounded-full text-lg transition-all duration-300 flex items-center justify-center gap-2"
              >
                <FaCog />
                配置API密钥
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AIAssistantSection; 