import React from 'react';
import { CopilotPopup } from '@copilotkit/react-ui';
import { CustomAssistantMessage } from './CopilotProvider';

/**
 * 简化的 CopilotPopup 包装器
 * 直接显示聊天界面，后台数据异步加载
 */
const SafeCopilotPopup = () => {
  return (
    <CopilotPopup
      labels={{
        title: "🤖 YNNX AI助手",
        initial: "您好！👋 我是YNNX AI平台的智能助手。\n\n✨ 我可以为您提供以下帮助：\n• 📚 **文档搜索** - 搜索开发工具安装指南和使用教程\n• 📄 **页面信息** - 查看当前页面的基本功能介绍\n• 🔧 **系统状态** - 检查平台服务运行状况\n• 💬 **智能问答** - 回答关于平台功能的常见问题\n\n💡 您可以这样使用我：\n• 🔍 \"搜索Cline插件安装教程\"\n• 📋 \"介绍当前页面功能\"\n• ⚙️ \"检查系统运行状态\"\n• ❓ \"平台有哪些开发工具\"\n\n有什么可以为您效劳的吗？",
        placeholder: "输入您的问题，支持文档搜索、页面介绍、状态检查等..."
      }}
      AssistantMessage={CustomAssistantMessage}
    />
  );
};

export default SafeCopilotPopup; 