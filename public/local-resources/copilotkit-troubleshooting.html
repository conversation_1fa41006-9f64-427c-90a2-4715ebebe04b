<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CopilotKit 故障排除 - 内网版本</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background: #f8fafc;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e2e8f0;
        }
        .logo {
            font-size: 2.5em;
            font-weight: bold;
            color: #dc2626;
            margin-bottom: 10px;
        }
        .subtitle {
            color: #64748b;
            font-size: 1.1em;
        }
        .problem-section {
            margin: 25px 0;
            padding: 20px;
            background: #fef2f2;
            border-radius: 8px;
            border-left: 4px solid #dc2626;
        }
        .solution-section {
            margin: 25px 0;
            padding: 20px;
            background: #f0fdf4;
            border-radius: 8px;
            border-left: 4px solid #16a34a;
        }
        .section-title {
            font-weight: bold;
            color: #1e293b;
            margin-bottom: 15px;
            font-size: 1.2em;
        }
        .problem-title {
            color: #dc2626;
        }
        .solution-title {
            color: #16a34a;
        }
        .code-block {
            background: #1e293b;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 0.9em;
            overflow-x: auto;
            margin: 10px 0;
        }
        .step-list {
            list-style: none;
            padding: 0;
            counter-reset: step-counter;
        }
        .step-list li {
            counter-increment: step-counter;
            padding: 10px 0;
            border-bottom: 1px solid #e2e8f0;
        }
        .step-list li:before {
            content: counter(step-counter);
            background: #3b82f6;
            color: white;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-weight: bold;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🔧 故障排除</div>
            <div class="subtitle">CopilotKit 内网版本常见问题解决方案</div>
        </div>

        <div class="problem-section">
            <div class="section-title problem-title">❌ 常见问题 1: AI 助手无响应</div>
            <p><strong>症状：</strong>点击 AI 助手按钮后没有反应，或者对话框无法加载。</p>
        </div>

        <div class="solution-section">
            <div class="section-title solution-title">✅ 解决方案</div>
            <ol class="step-list">
                <li>检查 LiteLLM 服务是否正常运行</li>
                <li>验证内网 API 配置是否正确</li>
                <li>确认浏览器控制台无错误信息</li>
                <li>检查网络连接到内网 AI 服务</li>
            </ol>
            <div class="code-block">
# 检查 LiteLLM 服务状态
curl http://your-internal-litellm-server:4000/health

# 检查 API 配置
cat .env | grep VITE_LITELLM_API_BASE
            </div>
        </div>

        <div class="problem-section">
            <div class="section-title problem-title">❌ 常见问题 2: 模型加载失败</div>
            <p><strong>症状：</strong>AI 助手提示"模型不可用"或"连接失败"。</p>
        </div>

        <div class="solution-section">
            <div class="section-title solution-title">✅ 解决方案</div>
            <ol class="step-list">
                <li>确认内网模型服务正常运行</li>
                <li>检查模型配置文件</li>
                <li>验证 API 密钥有效性</li>
                <li>测试模型 API 连通性</li>
            </ol>
            <div class="code-block">
# 测试模型 API
curl -X POST http://your-internal-api:4000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-api-key" \
  -d '{"model":"your-model","messages":[{"role":"user","content":"test"}]}'
            </div>
        </div>

        <div class="problem-section">
            <div class="section-title problem-title">❌ 常见问题 3: 权限认证失败</div>
            <p><strong>症状：</strong>提示"未授权访问"或"登录失败"。</p>
        </div>

        <div class="solution-section">
            <div class="section-title solution-title">✅ 解决方案</div>
            <ol class="step-list">
                <li>检查 LDAP 认证配置</li>
                <li>验证用户名和密码</li>
                <li>确认用户组权限设置</li>
                <li>检查会话是否过期</li>
            </ol>
            <div class="code-block">
# 检查 LDAP 连接
ldapsearch -x -H ldap://your-ldap-server:389 \
  -D "cn=admin,dc=example,dc=com" \
  -w password \
  -b "dc=example,dc=com"
            </div>
        </div>

        <div class="problem-section">
            <div class="section-title problem-title">❌ 常见问题 4: 页面加载缓慢</div>
            <p><strong>症状：</strong>页面加载时间过长，或者资源加载失败。</p>
        </div>

        <div class="solution-section">
            <div class="section-title solution-title">✅ 解决方案</div>
            <ol class="step-list">
                <li>检查内网带宽和延迟</li>
                <li>优化静态资源缓存</li>
                <li>启用 gzip 压缩</li>
                <li>检查 CDN 配置</li>
            </ol>
            <div class="code-block">
# 检查网络延迟
ping your-internal-server

# 检查资源大小
du -sh dist/assets/*

# 启用 nginx gzip
gzip on;
gzip_types text/css application/javascript;
            </div>
        </div>

        <div class="solution-section">
            <div class="section-title solution-title">📞 获取更多帮助</div>
            <p>如果以上解决方案无法解决您的问题，请：</p>
            <ol class="step-list">
                <li>联系内网管理员</li>
                <li>查看系统日志文件</li>
                <li>提交技术支持工单</li>
                <li>参考内网文档中心</li>
            </ol>
        </div>
    </div>
</body>
</html>
