<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CopilotKit 信息 - 内网版本</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background: #f8fafc;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e2e8f0;
        }
        .logo {
            font-size: 2.5em;
            font-weight: bold;
            color: #3b82f6;
            margin-bottom: 10px;
        }
        .subtitle {
            color: #64748b;
            font-size: 1.1em;
        }
        .info-section {
            margin: 25px 0;
            padding: 20px;
            background: #f1f5f9;
            border-radius: 8px;
            border-left: 4px solid #3b82f6;
        }
        .info-title {
            font-weight: bold;
            color: #1e293b;
            margin-bottom: 10px;
            font-size: 1.2em;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #e2e8f0;
        }
        .feature-list li:before {
            content: "✓";
            color: #10b981;
            font-weight: bold;
            margin-right: 10px;
        }
        .notice {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        .notice-title {
            font-weight: bold;
            color: #92400e;
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🤖 CopilotKit</div>
            <div class="subtitle">AI 助手开发框架 - 内网版本</div>
        </div>

        <div class="notice">
            <div class="notice-title">📢 内网部署说明</div>
            <p>您正在使用 CopilotKit 的内网版本。所有功能均已本地化，无需外部网络连接。</p>
        </div>

        <div class="info-section">
            <div class="info-title">🚀 主要功能</div>
            <ul class="feature-list">
                <li>智能对话组件 (CopilotChat)</li>
                <li>文本区域增强 (CopilotTextarea)</li>
                <li>侧边栏集成 (CopilotSidebar)</li>
                <li>弹窗对话 (CopilotPopup)</li>
                <li>自定义 AI 动作</li>
                <li>上下文感知对话</li>
                <li>多模型支持</li>
            </ul>
        </div>

        <div class="info-section">
            <div class="info-title">⚙️ 内网配置</div>
            <p>在内网环境中，CopilotKit 组件将：</p>
            <ul class="feature-list">
                <li>使用本地 AI 模型服务</li>
                <li>禁用外部 API 调用</li>
                <li>使用内网 LiteLLM 代理</li>
                <li>本地化所有静态资源</li>
            </ul>
        </div>

        <div class="info-section">
            <div class="info-title">📚 使用指南</div>
            <p>CopilotKit 已集成到 YNNX AI 平台中，提供以下功能：</p>
            <ul class="feature-list">
                <li>智能代码补全</li>
                <li>自然语言查询</li>
                <li>文档生成助手</li>
                <li>API 调用建议</li>
            </ul>
        </div>

        <div class="info-section">
            <div class="info-title">🔧 技术支持</div>
            <p>如需技术支持，请联系：</p>
            <ul class="feature-list">
                <li>内网管理员</li>
                <li>YNNX AI 平台技术团队</li>
                <li>查看本地文档中心</li>
            </ul>
        </div>
    </div>
</body>
</html>
