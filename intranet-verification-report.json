{"timestamp": "2025-07-07T06:51:47.778Z", "summary": {"total": 17, "passed": 16, "failed": 0, "warnings": 1, "success": true}, "details": [{"test": "构建文件检查", "status": "PASS", "message": "dist/index.html 存在", "details": null, "timestamp": "2025-07-07T06:51:47.676Z"}, {"test": "构建文件检查", "status": "PASS", "message": "dist/assets 存在", "details": null, "timestamp": "2025-07-07T06:51:47.676Z"}, {"test": "构建文件检查", "status": "PASS", "message": "dist/favicon.svg 存在", "details": null, "timestamp": "2025-07-07T06:51:47.676Z"}, {"test": "构建文件检查", "status": "PASS", "message": "dist/manifest.json 存在", "details": null, "timestamp": "2025-07-07T06:51:47.676Z"}, {"test": "构建文件检查", "status": "PASS", "message": "dist/sw.js 存在", "details": null, "timestamp": "2025-07-07T06:51:47.676Z"}, {"test": "外部链接检查", "status": "PASS", "message": "未发现有问题的外部链接", "details": null, "timestamp": "2025-07-07T06:51:47.774Z"}, {"test": "字体文件检查", "status": "PASS", "message": "fa-solid-900.woff2 存在", "details": null, "timestamp": "2025-07-07T06:51:47.774Z"}, {"test": "字体文件检查", "status": "PASS", "message": "fa-regular-400.woff2 存在", "details": null, "timestamp": "2025-07-07T06:51:47.774Z"}, {"test": "字体文件检查", "status": "PASS", "message": "fa-brands-400.woff2 存在", "details": null, "timestamp": "2025-07-07T06:51:47.774Z"}, {"test": "环境变量检查", "status": "PASS", "message": "VITE_INTRANET_MODE 已配置", "details": null, "timestamp": "2025-07-07T06:51:47.774Z"}, {"test": "环境变量检查", "status": "PASS", "message": "VITE_DISABLE_EXTERNAL_RESOURCES 已配置", "details": null, "timestamp": "2025-07-07T06:51:47.774Z"}, {"test": "环境变量检查", "status": "PASS", "message": "VITE_OFFLINE_MODE 已配置", "details": null, "timestamp": "2025-07-07T06:51:47.775Z"}, {"test": "Service Worker检查", "status": "PASS", "message": "Service Worker无外部依赖", "details": null, "timestamp": "2025-07-07T06:51:47.775Z"}, {"test": "依赖包检查", "status": "PASS", "message": "@copilotkit/react-core 已安装", "details": null, "timestamp": "2025-07-07T06:51:47.775Z"}, {"test": "依赖包检查", "status": "PASS", "message": "react-icons 已安装", "details": null, "timestamp": "2025-07-07T06:51:47.775Z"}, {"test": "依赖包检查", "status": "PASS", "message": "lucide-react 已安装", "details": null, "timestamp": "2025-07-07T06:51:47.775Z"}, {"test": "构建大小检查", "status": "WARN", "message": "构建大小较大: 454M", "details": "建议优化资源", "timestamp": "2025-07-07T06:51:47.778Z"}], "recommendations": ["检查警告项目并考虑优化", "在真实内网环境中进行功能测试", "配置内网DNS和反向代理"]}